# Development Philosophy

## Core Principles

**Leverage Existing Infrastructure** - Don't reinvent the wheel. Use proven services like Apify, Upstash, Vercel, Supabase, Slack, GitHub, OpenAI, Vercel AI SDK, shadcn/ui, Next.js, TypeScript, and Ultracite. These tools have solved complex problems - we just need to leverage them intelligently.

**Simplicity & Maintainability** - Keep the project simple and easy to understand. Follow single responsibility principles:
- Single file, single function, single responsibility
- Code should be DRY, easy to understand, and easy to maintain
- Fast development iteration is crucial during active development phase

**Focus Over Features** - As <PERSON> teaches, simplify ruthlessly. We use a **two-phase processing approach**:

**Phase 1 (Webhooks):** Fast minimal extraction + complete raw storage, programmatic and free 
**Phase 2 (AI):** Centralized extraction of all structured fields from raw data, AI-powered and paid

Pipeline stages:
1. **Job Sourcing** (Apify) → 2. **Minimal Extraction & Storage** (Webhooks → Supabase) → 3. **AI Processing** (QStash Scheduler) → 4. **Publishing** (Airtable) → 5. **Monitoring** (Multi-layer)

**Cloud-First Architecture** - Leverage existing cloud services so a single founder/developer can focus on core value creation rather than infrastructure management.

**Async Processing** - We never need real-time processing for job post data. If operations take time, that's acceptable - optimize for reliability over speed.

**Simplified Webhook Architecture** - Keep webhooks fast and simple (< 200ms) by extracting only minimal fields required for deduplication and storing complete raw data. Move all complex field extraction to the AI processing layer.

**End-to-End Functionality** - Every feature must work completely end-to-end, including customization capabilities and multi-platform publishing.

## Development Guidelines

- **Production Ready** - All code must be production-quality from day one
- **Type Safety** - Strict TypeScript throughout the entire codebase
- **Testing** - Comprehensive testing with real data validation
- **Documentation** - Clear, maintainable documentation as single source of truth
- **Monitoring** - Proactive monitoring and alerting for all critical paths

## Technical Pipeline Architecture

### Step 1: Job Data Ingestion ✅ PRODUCTION READY
**Apify Only** - Handles RSS/XML/APIs/web scraping and any other job data ingestion. Built-in retries, notifications, customizable and scalable.

**Current Status:** All 3 sources fully operational
- **Workable XML Feed** - 1000+ jobs/day via Apify actor
- **WeWorkRemotely RSS** - Remote jobs via custom Apify actor  
- **JobDataAPI** - Professional job data via Apify actor

### Step 2: Minimal Extraction & Direct Storage ✅ PRODUCTION READY
**Fast Webhook Processing** - Webhooks extract essential fields and save directly to Supabase:

**Webhook Phase (< 200ms):**
- Extract minimal fields: `title`, `company`, `external_id`, `source_url/apply_email`
- Generate reliable external_id for deduplication (e.g., `jobdataapi_24304799`)
- Store complete original data in `raw_sourced_job_data` field
- **Direct insert to Supabase** with `processing_status: 'pending'`
- **Database serves as queue** - no separate queueing system needed

**Benefits:**
- Fast webhook responses prevent timeouts
- Zero data loss - complete raw data preserved immediately
- Database-level deduplication using external_id uniqueness
- Source-agnostic approach scales to any data source

**Current Status:** All sources using direct database storage with pending status

### Step 3: AI Processing ✅ PRODUCTION READY
**QStash Scheduled AI Processing** - Automated batch processing that fetches pending jobs and updates them:

**QStash Processing Phase (Every 5 Minutes):**
- Fetches jobs with `processing_status: 'pending'` from Supabase (10 jobs per batch)
- Processes complete `raw_sourced_job_data` using OpenAI GPT-4o-mini and Zod schema
- Extracts 35+ structured fields: location, salary, remote, job_type, description, etc.
- **Updates same job record** with AI-extracted data and sets `processing_status: 'completed'`
- Handles complex parsing: HTML descriptions, salary ranges, location normalization

**Benefits:**
- **Single extraction point** - all complex logic centralized in AI
- **Complete context** - AI sees all original data, not pre-filtered
- **Consistent output** - same structured fields regardless of source
- **Reliable processing** - automatic retries and error handling via QStash
- **In-place updates** - no data movement, just enrichment

**Current Status:** Scheduled batch processing running every 5 minutes, processing 10 jobs per batch

### Step 4: Multi-Platform Publishing ✅ PRODUCTION READY
**Airtable Integration** with encrypted PAT storage, custom filtering, and automated job board management.

**Current Status:** Multi-board automation with encrypted credentials and custom filtering

### Step 5: Job Monitoring ✅ PRODUCTION READY
**3-layer monitoring pipeline** (HTTP HEAD → Phrase Matching → AI Classification) with automated status updates.

**Current Status:** Automated monitoring with intelligent retry logic and status tracking 
