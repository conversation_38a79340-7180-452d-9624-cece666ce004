# 🧪 Webhook Callback Testing Guide

**Complete production testing strategy for your webhook notification system**

## 🎯 **Testing Strategy Overview**

### **Phase 1: Pre-Deployment Verification**
1. **Health Check Tests** - Verify endpoints are accessible
2. **Environment Validation** - Confirm all secrets are configured
3. **QStash Configuration** - Test callback URL reachability

### **Phase 2: Live Production Testing**
1. **End-to-End Flow** - Trigger real Apify runs
2. **Success Scenario** - Monitor complete pipeline
3. **Failure Simulation** - Test error handling
4. **Performance Monitoring** - Analyze processing times

### **Phase 3: Verification & Monitoring**
1. **Log Analysis** - Parse debugging output
2. **Database Validation** - Verify job processing
3. **Slack Notifications** - Confirm alerts work

---

## 🔧 **Phase 1: Pre-Deployment Tests**

### **1.1 Health Check All Endpoints**

```bash
# Test webhook callback endpoint
curl -X GET https://bordfeed.com/api/webhook-callbacks

# Expected response:
{
  "message": "🔔 QStash webhook callbacks endpoint ready",
  "version": "v1.0.0",
  "features": ["✅ Success callback handling", ...]
}
```

### **1.2 Environment Variables Check**

**Check via Health API:**
```bash
curl -X GET https://bordfeed.com/api/health
```

**Look for:**
- ✅ `QSTASH_TOKEN` configured
- ✅ `QSTASH_CURRENT_SIGNING_KEY` configured  
- ✅ `QSTASH_NEXT_SIGNING_KEY` configured
- ✅ `SLACK_WEBHOOK_URL` configured

### **1.3 QStash Callback URL Reachability**

**Test from QStash perspective:**
```bash
# QStash needs to reach your callback URL
curl -X POST https://bordfeed.com/api/webhook-callbacks \
  -H "Content-Type: application/json" \
  -d '{"test": "reachability"}'

# Should return 401/403 (signature verification) not 404/500
```

---

## 🚀 **Phase 2: Live Production Testing**

### **2.1 Trigger End-to-End Test**

**Method 1: Manual Apify Run**
1. Go to [Apify Console](https://console.apify.com)
2. Find your JobDataAPI/Workable/WeWorkRemotely actors
3. Click "Start" to manually trigger a run
4. Monitor webhooks in real-time

**Method 2: Scheduled Run (Wait for Next)**
- Check your cron schedules in Apify
- Monitor when next automatic run occurs

### **2.2 Real-Time Log Monitoring**

**Monitor Vercel Logs:**
```bash
# In Vercel dashboard or CLI
vercel logs --follow

# Look for these log patterns:
🔔 [cb_*] QStash callback received
📋 [cb_*] Callback payload parsed  
✅ [cb_*] Processing SUCCESS callback
📤 Sending {source} batch to QStash with callbacks
📨 {source} QStash message sent successfully
```

**Monitor Slack Channel:**
- Watch for pipeline step notifications
- Success/failure alerts should appear

### **2.3 Database Verification**

**Check jobs were processed:**
```sql
-- Latest jobs from your sources
SELECT id, title, company, source_name, created_at, status
FROM jobs 
WHERE created_at > NOW() - INTERVAL '1 hour'
  AND source_name IN ('jobdata_api', 'workable', 'WeWorkRemotely')
ORDER BY created_at DESC;

-- Check processing stats
SELECT 
  source_name,
  COUNT(*) as total_jobs,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_jobs
FROM jobs 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY source_name;
```

---

## 🎯 **Phase 3: Targeted Testing Scenarios**

### **3.1 Success Scenario Test**

**Expected Flow:**
1. **Apify Actor Completes** → Slack notification ✅
2. **Webhook Received** → Log: `🔔 {source} webhook received`
3. **QStash Message Sent** → Log: `📤 Sending {source} batch to QStash with callbacks`
4. **Pipeline Processes Jobs** → Your existing pipeline logs
5. **Success Callback** → Log: `✅ [cb_*] Processing SUCCESS callback`
6. **Pipeline Step Logged** → Log: `📊 [cb_*] Parsed response data`

**Verification Commands:**
```bash
# Check callback success rate
grep "✅.*Processing SUCCESS callback" /var/log/bordfeed/*.log | wc -l
grep "❌.*Processing FAILURE callback" /var/log/bordfeed/*.log | wc -l

# Check job processing metrics
grep "📊.*Parsed response data" /var/log/bordfeed/*.log | tail -5
```

### **3.2 Failure Scenario Simulation**

**Method 1: Temporarily Break Pipeline**
```bash
# Temporarily disable pipeline-ingest endpoint (return 500)
# Edit route.ts to return early error for testing

# Expected logs:
❌ [cb_*] Processing FAILURE callback (DLQ)
💥 QStash message processing failed - sent to DLQ
```

**Method 2: Test QStash Rate Limits**
```bash
# Send multiple requests rapidly to trigger rate limiting
# Should see:
❌ QStash publish failed
  ├── status: 429
  ├── statusText: "Too Many Requests"
```

### **3.3 Performance Testing**

**Monitor Processing Times:**
```bash
# Check average processing times
grep "processingTime.*ms" /var/log/bordfeed/*.log | \
  grep -o '[0-9]\+ms' | \
  sort -n | tail -10

# Check batch sizes
grep "jobCount.*[0-9]" /var/log/bordfeed/*.log | \
  grep -o 'jobCount: [0-9]\+' | \
  sort -n | tail -10
```

---

## 🔍 **Using Available MCP Tools for Testing**

### **Browser Testing with Playwright**

**Test Dashboard Monitoring:**
```javascript
// Navigate to health dashboard
await page.goto('https://bordfeed.com/dashboard/health');

// Check callback endpoint status
const callbackStatus = await page.textContent('[data-testid="callback-status"]');
expect(callbackStatus).toBe('Healthy');

// Check recent pipeline activity
const recentJobs = await page.locator('[data-testid="recent-jobs"]').count();
expect(recentJobs).toBeGreaterThan(0);
```

### **Database Testing with Supabase MCP**

**Verify Job Processing:**
```sql
-- Check recent webhook processing
SELECT 
  id, title, company, source_name, 
  created_at, updated_at
FROM jobs 
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC 
LIMIT 20;

-- Verify deduplication worked
SELECT 
  source_name,
  COUNT(*) as total,
  COUNT(DISTINCT title || company) as unique_combinations
FROM jobs 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY source_name;
```

---

## 📊 **Success Metrics to Monitor**

### **Key Performance Indicators**

**1. Callback Success Rate**
- Target: >95% success rate
- Monitor: `grep "✅.*SUCCESS callback" logs vs "❌.*FAILURE callback" logs`

**2. End-to-End Processing Time**
- Target: <5 minutes from Apify completion to database storage
- Monitor: `processingTime` in logs + callback timing

**3. Job Processing Success**
- Target: >90% jobs successfully processed and stored
- Monitor: Database counts vs webhook job counts

**4. Error Recovery**
- Target: <1% jobs lost to DLQ (Dead Letter Queue)
- Monitor: DLQ notifications in Slack

### **Alert Conditions**

**Immediate Action Required:**
- 🚨 No callbacks received for >1 hour
- 🚨 Callback success rate <80%
- 🚨 Complete pipeline failure (0 jobs processed)
- 🚨 DLQ messages (jobs permanently failed)

**Monitor & Investigate:**
- ⚠️ Callback success rate <95%
- ⚠️ Processing time >10 minutes
- ⚠️ High failure rate (>25%) in batch processing

---

## 🎯 **Quick Test Commands**

### **After Deployment - Quick Verification**

```bash
# 1. Test callback endpoint is live
curl -I https://bordfeed.com/api/webhook-callbacks

# 2. Check latest job processing
# (Use Supabase MCP to query recent jobs)

# 3. Monitor logs for callback activity
tail -f /var/log/bordfeed/*.log | grep "cb_"

# 4. Verify QStash connectivity
curl -X GET https://bordfeed.com/api/health | grep -i qstash
```

### **Success Confirmation Checklist**

After triggering a test run, confirm:

- [ ] **Apify Webhook Received** - Log: `🔔 {source} webhook received`
- [ ] **QStash Message Sent** - Log: `📤 Sending {source} batch to QStash with callbacks`
- [ ] **Callback Received** - Log: `🔔 [cb_*] QStash callback received`
- [ ] **Success Processing** - Log: `✅ [cb_*] Processing SUCCESS callback`
- [ ] **Jobs in Database** - SQL: `SELECT COUNT(*) FROM jobs WHERE created_at > NOW() - INTERVAL '1 hour'`
- [ ] **Slack Notification** - Pipeline step notifications in Slack
- [ ] **No DLQ Messages** - No `💥 QStash message processing failed`

---

## 🚨 **Troubleshooting Common Issues**

### **No Callbacks Received**
1. Check QStash can reach `https://bordfeed.com/api/webhook-callbacks`
2. Verify `QSTASH_*` environment variables are set
3. Check webhook handlers include callback URLs

### **Signature Verification Failed**
1. Verify `QSTASH_CURRENT_SIGNING_KEY` matches QStash console
2. Check `QSTASH_NEXT_SIGNING_KEY` for key rotation
3. Ensure headers include proper QStash signature

### **High Failure Rate**
1. Check pipeline-ingest endpoint is healthy
2. Monitor database connection status
3. Verify AI extraction service availability

**Ready to deploy and test! 🚀** 