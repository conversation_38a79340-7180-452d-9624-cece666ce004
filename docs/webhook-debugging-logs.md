# 🐛 Webhook Notification Debugging Guide

**Complete logging solution for troubleshooting and monitoring your webhook notification pipeline**

## 🎯 **Debug Coverage Added**

### **1. QStash Callback Handler (`/api/webhook-callbacks`)**

#### **Incoming Request Logging**
```bash
🔔 [cb_1234567890_xyz123] QStash callback received
  ├── timestamp: "2025-01-19T10:30:45.123Z"
  ├── headers: { "x-signature": "...", "content-type": "application/json" }
  ├── url: "https://bordfeed.com/api/webhook-callbacks"
  └── method: "POST"
```

#### **Payload Analysis**
```bash
📋 [cb_1234567890_xyz123] Callback payload parsed
  ├── status: 200
  ├── method: "POST"
  ├── url: "https://bordfeed.com/api/pipeline-ingest"
  ├── sourceMessageId: "msg_abc123def456"
  ├── retried: 0
  ├── createdAt: "2025-01-19T10:30:30.000Z"
  ├── hasBody: true
  ├── bodyLength: 1248
  ├── hasDlqId: false (or true for failures)
  └── keys: ["status", "method", "url", ...]
```

#### **Success Callback Processing**
```bash
✅ [cb_1234567890_xyz123] Processing SUCCESS callback
  ├── status: 200
  ├── messageId: "msg_abc123def456"
  ├── retryCount: 0
  ├── processingTime: 2456ms
  └── targetUrl: "https://bordfeed.com/api/pipeline-ingest"

📦 [cb_1234567890_xyz123] Decoding response body
  ├── originalLength: 1248 (base64)
  ├── decodedLength: 936 (JSON)
  └── decodedPreview: "{"success":true,"summary":{"total":25..."

📊 [cb_1234567890_xyz123] Parsed response data
  ├── hasData: true
  ├── hasSummary: true
  ├── summaryKeys: ["total", "successful", "failed", "duplicates", ...]
  ├── totalJobs: 25
  ├── successfulJobs: 23
  └── failedJobs: 2
```

#### **Failure Callback Processing**
```bash
❌ [cb_1234567890_xyz123] Processing FAILURE callback (DLQ)
  ├── status: 500
  ├── messageId: "msg_abc123def456"
  ├── dlqId: "dlq_failed_xyz789"
  ├── retryCount: 3
  ├── maxRetries: 3
  ├── processingTime: 45000ms
  ├── targetUrl: "https://bordfeed.com/api/pipeline-ingest"
  └── method: "POST"
```

### **2. Webhook Handlers (Source-Specific)**

#### **JobDataAPI Webhook (`/api/jobdata-webhook`)**
```bash
📤 Sending QStash message with callbacks
  ├── batchId: "jobdata-run123-batch-1"
  ├── jobCount: 50
  ├── targetUrl: "https://bordfeed.com/api/pipeline-ingest"
  ├── callbackUrl: "https://bordfeed.com/api/webhook-callbacks"
  ├── failureCallbackUrl: "https://bordfeed.com/api/webhook-callbacks"
  └── payloadSize: 12456 bytes

📨 QStash message sent successfully
  ├── batchId: "jobdata-run123-batch-1"
  ├── messageId: "msg_qst_abc123"
  ├── responseStatus: 200
  ├── callbackConfigured: true
  ├── failureCallbackConfigured: true
  └── qstashHeaders: { "upstash-message-id": "...", ... }
```

#### **Workable Webhook (`/api/workable-webhook`)**
```bash
📤 Sending Workable batch to QStash with callbacks
  ├── batchId: "workable-run456-batch-1"
  ├── jobCount: 75
  ├── targetUrl: "https://bordfeed.com/api/pipeline-ingest"
  ├── callbackUrl: "https://bordfeed.com/api/webhook-callbacks"
  ├── failureCallbackUrl: "https://bordfeed.com/api/webhook-callbacks"
  └── payloadSize: 18932 bytes

📨 Workable QStash message sent successfully
  ├── batchId: "workable-run456-batch-1"
  ├── messageId: "msg_qst_def456"
  ├── jobCount: 75
  ├── callbackConfigured: true
  └── failureCallbackConfigured: true
```

#### **WeWorkRemotely Webhook (`/api/wwr-webhook`)**
```bash
📤 Sending WeWorkRemotely batch to QStash with callbacks
  ├── batchId: "wwr-run789-batch-1"
  ├── jobCount: 30
  ├── targetUrl: "https://bordfeed.com/api/pipeline-ingest"
  ├── callbackUrl: "https://bordfeed.com/api/webhook-callbacks"
  ├── failureCallbackUrl: "https://bordfeed.com/api/webhook-callbacks"
  └── payloadSize: 9124 bytes

📨 WeWorkRemotely batch 1 sent via QStash with callbacks
  ├── batchSize: 30
  ├── messageId: "msg_qst_ghi789"
  ├── batchId: "wwr-run789-batch-1"
  ├── responseStatus: 200
  ├── callbackConfigured: true
  ├── failureCallbackConfigured: true
  └── qstashHeaders: { "upstash-message-id": "...", ... }
```

## 🔍 **Debugging Workflow**

### **End-to-End Trace Example**

1. **Apify Actor Completes** → Slack notification ✅
2. **Webhook Received** → Log: `🔔 {source} webhook received`
3. **QStash Message Sent** → Log: `📤 Sending {source} batch to QStash with callbacks`
4. **QStash Processes** → Log: `📨 {source} QStash message sent successfully`
5. **Pipeline Processes** → (Your existing pipeline logs)
6. **Callback Received** → Log: `🔔 [callbackId] QStash callback received`
7. **Success/Failure** → Log: `✅ Processing SUCCESS callback` or `❌ Processing FAILURE callback`

### **Troubleshooting Commands**

#### **Filter by Callback ID**
```bash
# Follow a specific callback through the entire flow
grep "cb_1234567890_xyz123" /var/log/bordfeed/*.log
```

#### **Monitor QStash Sends**
```bash
# Watch all QStash message sending
grep "📤 Sending.*batch to QStash" /var/log/bordfeed/*.log
```

#### **Track Callback Success Rate**
```bash
# Count successful vs failed callbacks
grep "✅ Processing SUCCESS callback" /var/log/bordfeed/*.log | wc -l
grep "❌ Processing FAILURE callback" /var/log/bordfeed/*.log | wc -l
```

#### **Monitor High Failure Rates**
```bash
# Watch for batch processing issues
grep "⚠️ High failure rate detected" /var/log/bordfeed/*.log
```

## 🚨 **Error Scenarios & Logs**

### **QStash Publish Failures**
```bash
❌ QStash publish failed
  ├── status: 429
  ├── statusText: "Too Many Requests"
  ├── errorBody: "Rate limit exceeded. Retry after 60 seconds"
  ├── batchIndex: 2
  └── batchId: "jobdata-run123-batch-3"
```

### **Callback Parsing Errors**
```bash
⚠️ [cb_1234567890_xyz123] Failed to parse response body as JSON
  ├── error: "Unexpected token < in JSON at position 0"
  ├── decodedLength: 1456
  └── decodedPreview: "<!DOCTYPE html><html><head><title>Error</title>..."
```

### **DLQ (Dead Letter Queue) Failures**
```bash
💥 QStash message processing failed - sent to DLQ
  ├── batchId: "workable-run456-batch-2"
  ├── jobCount: 75
  ├── messageId: "msg_qst_failed_123"
  ├── dlqId: "dlq_failed_xyz789"
  ├── retryCount: 3
  ├── finalStatus: 500
  ├── failureUrl: "https://bordfeed.com/api/pipeline-ingest"
  ├── processingTime: 45000ms
  ├── impact: "Jobs lost - manual intervention required"
  └── action: "Check DLQ: https://console.upstash.com/qstash/dlq/dlq_failed_xyz789"
```

## 📊 **Performance Monitoring**

### **Key Metrics Logged**
- **Processing Time**: Webhook → QStash → Callback duration
- **Payload Sizes**: Track message size impacts
- **Retry Counts**: Monitor QStash retry behavior
- **Batch Sizes**: Optimize batching strategy
- **Failure Rates**: Pipeline health monitoring

### **Success Rate Calculation**
```bash
# Get success percentage for the last hour
success=$(grep -c "✅ Processing SUCCESS callback" /var/log/bordfeed/$(date +%Y-%m-%d-%H).log)
total=$(grep -c "🔔.*QStash callback received" /var/log/bordfeed/$(date +%Y-%m-%d-%H).log)
echo "Success Rate: $(( success * 100 / total ))%"
```

## 🛡️ **Production Readiness**

### **What You Can Now Monitor**
✅ **Complete end-to-end visibility** from Apify → QStash → Callbacks  
✅ **Detailed error diagnostics** with specific failure reasons  
✅ **Performance metrics** for optimization  
✅ **Callback correlation** with unique tracking IDs  
✅ **Batch processing health** with job counts and timing  
✅ **DLQ monitoring** for failed message recovery  

### **Next Steps**
1. **Deploy** and monitor initial callback traffic
2. **Verify** callback URLs are reachable from QStash
3. **Test** both success and failure scenarios
4. **Monitor** logs for any unexpected patterns
5. **Optimize** based on performance data

**You now have comprehensive debugging coverage! 🎉** 