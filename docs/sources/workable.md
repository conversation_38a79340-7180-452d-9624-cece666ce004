# Workable XML Feed Source Documentation

## Overview

Workable XML Feed is an enterprise-scale job aggregation source that processes a massive 458MB XML file containing job postings from companies using the Workable platform. This source provides the highest volume of jobs but requires the longest processing time due to its scale.

## Architecture

```
Dashboard Trigger → Apify Actor → XML Processing → Webhook → Database → Dashboard
     ↓                ↓              ↓            ↓         ↓          ↓
   Manual Run    Download 458MB   Parse & Filter  Process   Store    Display
```

## Configuration

### Environment Variables

**⚠️ Important**: Environment variables are stored in **Vercel dashboard**, not in `.env` files.

For local development:
```bash
# Pull secrets from Vercel to .env.local
pnpm run env:pull
```

Required variables in Vercel:
```bash
APIFY_TOKEN=apify_api_...        # Apify API token
WORKABLE_ACTOR_ID=ccu7fefjONjsRoaBN  # Workable XML actor ID
SUPABASE_URL=...                 # Database connection
SUPABASE_SERVICE_ROLE_KEY=...    # Database access
```

### XML Feed Source
- **Feed URL**: `https://www.workable.com/boards/workable.xml`
- **File Size**: ~458MB (compressed)
- **Update Frequency**: Real-time (companies post continuously)
- **Content Type**: Global job listings from Workable platform

## Data Flow

### 1. Manual Trigger
```typescript
// From /dashboard/sources
POST /api/sources/workable/run
```

### 2. Apify Actor Processing
- **Actor**: `craftled/workable-jobs-scraper`
- **Download Phase**: 13.8 seconds (458MB at 33.3 MB/s)
- **Processing Phase**: 3-5 minutes (parsing 90k+ jobs)
- **Output**: 500-1000+ jobs per run
- **Filtering**: English-only, 7-day freshness filter

### 3. Webhook Processing
- **Endpoint**: `/api/workable-webhook`
- **Data Source**: Uses actor run ID to fetch dataset
- **Validation**: Field mapping and data validation
- **Storage**: Bulk insert to Supabase

### 4. Smart Filtering
```typescript
{
  dateFilter: "7 days",          // Only recent jobs
  languageFilter: "English-only", // Filter non-English postings
  limit: 1000,                   // Maximum jobs per run
  batchSize: 50,                 // Processing batch size
  englishOnly: true,             // Language detection
  ageFilter: "7d"                // Freshness filter
}
```

## Sample Job Data

### Input (from XML Feed)
```xml
<job>
  <title>Software Engineer</title>
  <company>TechStart Inc</company>
  <location>San Francisco, CA</location>
  <description><![CDATA[
    <p>We are looking for a talented Software Engineer...</p>
    <p>Requirements: JavaScript, React, Node.js</p>
    <p>Benefits: Health insurance, 401k, Remote work</p>
  ]]></description>
  <apply_url>https://apply.workable.com/j/ABC123XYZ</apply_url>
  <published_date>2025-07-22T10:00:00Z</published_date>
  <department>Engineering</department>
</job>
```

### Output (to Database)
```json
{
  "id": "uuid-generated",
  "title": "Software Engineer",
  "company": "TechStart Inc",
  "description": "",
  "apply_url": "https://apply.workable.com/j/ABC123XYZ",
  "source_type": "workable",
  "source_name": "Workable",
  "processing_status": "pending",
  "created_at": "2025-07-22T10:00:00Z",
  "raw_sourced_job_data": {...}
}
```

## Performance Metrics

- **Runtime**: 5-10 minutes per run
- **Job Volume**: 500-1000+ jobs per run (highest volume source)
- **Success Rate**: 95%+ when properly configured
- **Cost**: ~$0.015-0.020 per run (higher due to processing time)
- **Frequency**: On-demand (manual trigger)
- **Processing Speed**: 1200+ jobs/second parsing rate

## Processing Details

### Download Phase
```
📥 DOWNLOADING XML FILE...
🚀 Using direct connection (faster for large files)
📡 Downloading from: https://www.workable.com/boards/workable.xml
✅ Download complete: 458.0MB in 13.8s (avg: 33.3 MB/s)
```

### Processing Phase
```
⚡ PROCESSING XML with optimized lxml techniques...
🎯 Target: 1000 jobs, Date filter: 7 days, English only: True
⚡ Progress: 10,000 parsed (1244/sec), 50 fresh (0.5%)
⚡ Progress: 50,000 parsed (1228/sec), 250 fresh (0.5%)
⚡ Progress: 90,000 parsed (1209/sec), 500 fresh (0.6%)
💾 Saved batch: 50 jobs (total saved: 500)
```

## Job Categories & Quality

### Job Categories
Workable includes diverse job categories:
- **Technology**: Software Engineering, DevOps, Data Science
- **Healthcare**: Medical professionals, Therapists, Nursing
- **Business**: Sales, Marketing, Operations, Finance
- **Education**: Teachers, Administrators, Trainers
- **Creative**: Design, Writing, Media production

### Geographic Distribution
- **US**: Majority of positions (60%+)
- **Europe**: UK, Germany, Netherlands (25%+)
- **Global**: Remote and international positions (15%+)

### Job Types
- **Full-time**: 80%+
- **Part-time**: 15%
- **Contract**: 5%
- **Remote**: 30%+ (growing trend)

## Troubleshooting

### Common Issues

#### 1. "Long processing time (5+ minutes)"
**Cause**: This is normal - Workable processes 458MB XML file
**Expectation**: 
- Download: ~14 seconds
- Processing: 3-5 minutes
- Total: 5-10 minutes

#### 2. "Actor timeout or memory issues"
**Cause**: Large file processing hitting resource limits
**Solution**: 
- Increase actor memory allocation
- Check Apify account resource limits
- Monitor actor resource usage

#### 3. "Low job count despite large XML file"
**Cause**: Aggressive filtering (7-day age, English-only)
**Explanation**: 90k+ jobs → filtered to 500-1000 fresh, English jobs

#### 4. "Webhook timeout or 404"
**Cause**: Dataset cleanup or webhook timing issues
**Solution**: Ensure webhook uses actor run ID approach

### Debugging Steps

1. **Check Actor Status**
```bash
curl -H "Authorization: Bearer $APIFY_TOKEN" \
  "https://api.apify.com/v2/acts/ccu7fefjONjsRoaBN/runs?limit=1"
```

2. **Monitor Live Processing**
- Check Apify console "Live view" tab
- Monitor log output for processing progress
- Watch for language filtering and date filtering

3. **Test Webhook**
```bash
curl -X POST "http://localhost:3001/api/workable-webhook" \
  -H "Content-Type: application/json" \
  -d '{"eventType":"ACTOR.RUN.SUCCEEDED","eventData":{"actorRunId":"RECENT_RUN_ID"}}'
```

4. **Check Database**
```sql
SELECT COUNT(*) FROM jobs WHERE source_name = 'Workable';
SELECT * FROM jobs WHERE source_name = 'Workable' ORDER BY created_at DESC LIMIT 10;
```

## Monitoring

### Real-time Monitoring
During runs, monitor:
- **Download Progress**: MB downloaded vs 458MB total
- **Parsing Progress**: Jobs parsed per second
- **Filter Results**: Fresh jobs found vs total parsed
- **Memory Usage**: Actor resource consumption

### Health Checks
- **XML Feed Availability**: Monitor Workable.com XML endpoint
- **Actor Performance**: Track processing speed and completion
- **Data Quality**: Validate job freshness and English filtering
- **Resource Usage**: Monitor memory and processing time

### Alerts
- Run duration > 15 minutes (potential timeout)
- Memory usage > 80% (resource limits)
- Job output < 300 jobs (low volume alert)
- XML download failures (feed unavailable)

## Optimization

### Performance Tuning
```typescript
{
  limit: 1000,           // Optimize for quality over quantity
  batchSize: 50,         // Efficient batch processing
  ageFilter: "7d",       // Focus on fresh jobs
  englishOnly: true,     // Reduce processing overhead
  proxy: false           // Direct connection for speed
}
```

### Resource Management
- **Memory**: 1024 MB allocation recommended
- **Timeout**: 3600 seconds (1 hour) for safety
- **Build**: Use latest version for performance improvements

## Maintenance

### Regular Tasks
- **Daily**: Monitor processing time and job volume
- **Weekly**: Review job quality and category distribution
- **Monthly**: Check XML feed performance and reliability
- **Quarterly**: Optimize filtering parameters based on results

### Updates
- Actor version updates via Apify console
- XML feed structure changes (monitor Workable announcements)
- Filter parameter adjustments based on job market changes

## Comparison with Other Sources

| Metric           | Workable      | JobDataAPI | WeWorkRemotely RSS |
| ---------------- | ------------- | ---------- | ------------------ |
| **Volume**       | ⭐⭐⭐⭐⭐ (1000+) | ⭐⭐⭐⭐ (100) | ⭐⭐⭐ (90)           |
| **Speed**        | ⭐⭐ (5-10min)  | ⭐⭐⭐⭐ (6s)  | ⭐⭐⭐⭐⭐ (5s)         |
| **Diversity**    | ⭐⭐⭐⭐⭐         | ⭐⭐⭐⭐       | ⭐⭐⭐                |
| **Global Reach** | ⭐⭐⭐⭐⭐         | ⭐⭐⭐        | ⭐⭐                 |
| **Enterprise**   | ⭐⭐⭐⭐⭐         | ⭐⭐⭐        | ⭐⭐                 |

## API Reference

### Trigger Run
```http
POST /api/sources/workable/run
Content-Type: application/json

Response:
{
  "success": true,
  "message": "Workable XML Feed actor started successfully",
  "runId": "abc123...",
  "estimatedDuration": "5-10 minutes"
}
```

### Webhook Handler
```http
POST /api/workable-webhook
Content-Type: application/json

Body:
{
  "eventType": "ACTOR.RUN.SUCCEEDED",
  "eventData": {"actorRunId": "abc123..."},
  "resource": {"id": "abc123...", "status": "SUCCEEDED"}
}

Response:
{
  "success": true,
  "runId": "abc123...",
  "jobsReceived": 800,
  "jobsSaved": 800,
  "jobsSkipped": 0,
  "processingTime": "6234ms"
}
```

## Related Files

- **Trigger**: `/app/api/sources/workable/run/route.ts`
- **Health Check**: `/app/api/sources/workable/health/route.ts`
- **Webhook**: `/app/api/workable-webhook/route.ts`
- **Constants**: `/lib/constants.ts`
- **Types**: `/lib/types.ts`

## Best Practices

1. **Timing**: Run during off-peak hours (processing intensive)
2. **Frequency**: 1-2 times daily maximum (high resource usage)
3. **Monitoring**: Always monitor processing logs for issues
4. **Resources**: Ensure adequate Apify resources before running

## Support

For issues with Workable XML Feed integration:
1. Check XML feed availability at workable.com
2. Monitor actor logs for processing progress
3. Verify actor resource allocation and limits
4. Check webhook processing and database insertion

## External Dependencies

- **Workable.com**: XML feed provider (enterprise platform)
- **Apify Platform**: High-memory actor execution
- **Supabase**: Bulk data storage
- **Next.js API**: Webhook integration 