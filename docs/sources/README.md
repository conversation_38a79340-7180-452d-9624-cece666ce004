# Bordfeed Job Sources Documentation

## Overview

This directory contains comprehensive documentation for all job sources integrated into Bordfeed. Each source has unique characteristics, processing requirements, and maintenance considerations.

## Available Sources

### 🚀 [JobDataAPI](./jobdata-api.md)
High-quality job aggregation service with structured data output.

**Quick Stats:**
- **Volume**: 100 jobs/run
- **Speed**: 6 seconds  
- **Type**: API → Webhook
- **Quality**: ⭐⭐⭐⭐⭐

### 📡 [WeWorkRemotely RSS](./weworkremotely-rss.md)
Direct RSS feed integration for remote-focused job listings.

**Quick Stats:**
- **Volume**: 93 jobs/run
- **Speed**: 5 seconds
- **Type**: RSS → Direct Storage
- **Quality**: ⭐⭐⭐⭐⭐

### 🏢 [Workable XML Feed](./workable.md)
Enterprise-scale XML processing for maximum job volume.

**Quick Stats:**
- **Volume**: 1000+ jobs/run
- **Speed**: 5-10 minutes
- **Type**: XML → Webhook
- **Quality**: ⭐⭐⭐⭐

## Quick Comparison

| Source             | Volume | Speed | Reliability | Remote Focus | Enterprise |
| ------------------ | ------ | ----- | ----------- | ------------ | ---------- |
| **JobDataAPI**     | ⭐⭐⭐⭐   | ⭐⭐⭐⭐  | ⭐⭐⭐⭐⭐       | ⭐⭐⭐          | ⭐⭐⭐        |
| **WeWorkRemotely** | ⭐⭐⭐    | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐       | ⭐⭐⭐⭐⭐        | ⭐⭐         |
| **Workable**       | ⭐⭐⭐⭐⭐  | ⭐⭐    | ⭐⭐⭐⭐        | ⭐⭐           | ⭐⭐⭐⭐⭐      |

## System Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐    ┌──────────────┐
│   Dashboard     │───▶│ Apify Actors │───▶│  Webhooks   │───▶│   Supabase   │
│   /sources      │    │              │    │             │    │   Database   │
└─────────────────┘    └──────────────┘    └─────────────┘    └──────────────┘
        │                       │                   │                 │
        ▼                       ▼                   ▼                 ▼
    Manual Trigger        Data Processing      Validation &       Job Storage
                                               Mapping
```

## Current Performance

### Production Metrics (July 2025)
- **Total Sources**: 3 active
- **Total Jobs Processed**: 1,193 jobs
- **Success Rate**: 99%+
- **Average Processing Time**: 2-8 minutes

### Source Breakdown
| Source         | Jobs  | Percentage | Status   |
| -------------- | ----- | ---------- | -------- |
| Workable       | 1,000 | 83.8%      | ✅ Active |
| JobDataAPI     | 100   | 8.4%       | ✅ Active |
| WeWorkRemotely | 93    | 7.8%       | ✅ Active |

## Data Pipeline

### 1. **Ingestion Phase**
- Manual trigger from dashboard
- Apify actors fetch raw data
- Different sources use different protocols (API, RSS, XML)

### 2. **Processing Phase**
```typescript
Raw Data → Field Mapping → Validation → Database Storage
    ↓            ↓            ↓             ↓
  Source      Standard     Required      Supabase
 Specific     Schema       Fields        Jobs Table
```

### 3. **Storage Schema**
```sql
CREATE TABLE jobs (
  id UUID PRIMARY KEY,
  title TEXT NOT NULL,
  company TEXT,
  description TEXT NOT NULL DEFAULT '',
  apply_url TEXT,
  source_type TEXT NOT NULL,
  source_name TEXT NOT NULL,
  processing_status TEXT DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  raw_sourced_job_data JSONB
);
```

## Integration Patterns

### Type 1: API + Webhook (JobDataAPI)
```
Trigger → Apify Actor → External API → Webhook → Database
```
- **Pros**: Structured data, reliable
- **Cons**: Webhook dependency

### Type 2: Direct Processing (WeWorkRemotely RSS)
```
Trigger → Apify Actor → RSS Feed → Direct Storage → Database
```
- **Pros**: No webhook dependency, fast
- **Cons**: Limited to RSS format

### Type 3: Bulk Processing (Workable XML)
```
Trigger → Apify Actor → Large XML → Webhook → Bulk Insert
```
- **Pros**: Highest volume, enterprise data
- **Cons**: Longest processing time

## Environment Configuration

### Required Variables

**⚠️ Critical**: Environment variables are stored in **Vercel dashboard** as the primary source of truth.

```bash
# Local development setup
pnpm run env:pull  # Syncs from Vercel to .env.local

# Variables stored in Vercel:
APIFY_TOKEN=apify_api_...

# Source-Specific Actor IDs
JOBDATA_ACTOR_ID=GxymxREtbsUduUFEO
WWR_ACTOR_ID=4q6w747EBIDBnPMG1
WORKABLE_ACTOR_ID=ccu7fefjONjsRoaBN

# Database Configuration
SUPABASE_URL=https://...
SUPABASE_SERVICE_ROLE_KEY=...
```

### Production Setup
1. **Vercel Secrets (Primary)**: All environment variables stored in Vercel dashboard
2. **Local Development**: Use `pnpm run env:pull` to sync from Vercel to `.env.local`
3. **Secrets Management**: Generated via `pnpm run generate-secrets`
4. **No .env Files**: Environment variables are NOT stored in `.env` files

## Monitoring & Health

### Dashboard Monitoring
- **Sources Page**: `/dashboard/sources` - Real-time status
- **Jobs Page**: `/dashboard/jobs` - Processed job listings
- **Health Page**: `/dashboard/health` - System health checks

### Health Check Endpoints
```bash
GET /api/sources/jobdata-api/health
GET /api/sources/wwr-rss/health  
GET /api/sources/workable/health
```

### Key Metrics
- **Run Success Rate**: >95% target
- **Processing Time**: Source-specific SLAs
- **Job Volume**: Expected ranges per source
- **Data Quality**: Field completeness validation

## Troubleshooting Guide

### Common Issues

#### 1. **"No jobs appearing after run"**
**Check Order:**
1. Actor completion in Apify console
2. Webhook logs in server console
3. Database job count via SQL query
4. Dashboard refresh and API response

#### 2. **"Webhook timeout or 404"**
**Solutions:**
- Use actor run ID instead of dataset ID
- Check webhook URL configuration in actors
- Verify environment variables

#### 3. **"Actor fails or times out"**
**Diagnosis:**
- Check Apify account limits
- Monitor actor resource usage
- Review actor logs for specific errors

### Debugging Commands

```bash
# Check database directly
psql $DATABASE_URL -c "SELECT source_name, COUNT(*) FROM jobs GROUP BY source_name;"

# Test webhook locally
curl -X POST "http://localhost:3001/api/jobdata-webhook" \
  -H "Content-Type: application/json" \
  -d '{"eventType":"ACTOR.RUN.SUCCEEDED","eventData":{"actorRunId":"TEST"}}'

# Check Apify actor status
curl -H "Authorization: Bearer $APIFY_TOKEN" \
  "https://api.apify.com/v2/acts/$ACTOR_ID/runs?limit=1"
```

## Development Workflow

### Adding New Sources

1. **Create Actor** in Apify platform
2. **Add Environment Variables** (actor ID, etc.)
3. **Create API Endpoints**: `/api/sources/{source}/run` and `/api/sources/{source}/health`
4. **Create Webhook Handler**: `/api/{source}-webhook`
5. **Update Field Mapping**: Add to `/lib/job-validation.ts`
6. **Add to Dashboard**: Update sources configuration
7. **Create Documentation**: Add to `/docs/sources/`

### Testing Checklist

- [ ] Manual trigger works from dashboard
- [ ] Actor completes successfully in Apify
- [ ] Webhook processes data correctly
- [ ] Jobs appear in database
- [ ] Jobs display in dashboard
- [ ] Health checks return correct status

## Support & Maintenance

### Weekly Tasks
- Review job volume and quality across sources
- Check for any failed runs or errors
- Monitor processing times and performance

### Monthly Tasks
- Review Apify usage and costs
- Update actor versions if available
- Analyze job market trends from data

### Quarterly Tasks
- Evaluate new job sources for integration
- Optimize field mapping and validation rules
- Review and update documentation

## Related Documentation

- **[Environment Variables](../environment-variables.md)**: Complete variable reference
- **[Webhook Debugging](../webhook-debugging-logs.md)**: Webhook troubleshooting
- **[Roadmap](../ROADMAP.md)**: Future source integrations

## Quick Reference

### File Locations
```
app/api/sources/           # Source trigger endpoints
app/api/*-webhook/         # Webhook handlers
lib/job-validation.ts      # Field mapping logic
lib/constants.ts           # Source configuration
docs/sources/              # This documentation
```

### Support Contacts
- **Technical Issues**: Check individual source documentation
- **Apify Problems**: Contact Apify support or check status page
- **Database Issues**: Check Supabase dashboard and logs 