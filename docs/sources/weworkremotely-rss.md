# WeWorkRemotely RSS Source Documentation

## Overview

WeWorkRemotely RSS is a direct RSS feed integration that scrapes job postings from WeWorkRemotely.com. Unlike other sources, it processes jobs directly without webhooks, making it one of the fastest and most reliable sources in our system.

## Architecture

```
Dashboard Trigger → Apify Actor → Direct Processing → Database → Dashboard
     ↓                ↓              ↓              ↓          ↓
   Manual Run    Fetch RSS Feed   Parse & Store   Store    Display
```

## Configuration

### Environment Variables

**⚠️ Important**: Environment variables are stored in **Vercel dashboard**, not in `.env` files.

For local development:
```bash
# Pull secrets from Vercel to .env.local
pnpm run env:pull
```

Required variables in Vercel:
```bash
APIFY_TOKEN=apify_api_...        # Apify API token
WWR_ACTOR_ID=4q6w747EBIDBnPMG1   # WeWorkRemotely RSS actor ID
SUPABASE_URL=...                 # Database connection
SUPABASE_SERVICE_ROLE_KEY=...    # Database access
```

### RSS Feed Source
- **Feed URL**: WeWorkRemotely.com RSS feed
- **Update Frequency**: Real-time (on-demand)
- **Content Type**: Remote job listings only

## Data Flow

### 1. Manual Trigger
```typescript
// From /dashboard/sources
POST /api/sources/wwr-rss/run
```

### 2. Apify Actor Processing
- **Actor**: `craftled/weworkremotely-rss-scraper`
- **Runtime**: ~5 seconds
- **Output**: 80-100 jobs per run
- **Processing**: Direct RSS parsing and job extraction

### 3. Direct Database Storage
- **No Webhook**: Jobs stored directly during actor run
- **Real-time**: Immediate availability in dashboard
- **Validation**: Built-in RSS field validation

### 4. Data Mapping
```typescript
{
  title: extractedTitle,
  company: extractedCompany,
  description: "", // Placeholder - extracted via AI later
  source_url: rssItemLink,
  apply_url: extractedApplyUrl || rssItemLink,
  source_type: "wwr_rss",
  source_name: "WeWorkRemotely",
  processing_status: "pending",
  raw_sourced_job_data: rssItem // Complete RSS item data
}
```

## Sample Job Data

### Input (from RSS Feed)
```xml
<item>
  <title>Senior Frontend Developer - RemoteCorp</title>
  <link>https://weworkremotely.com/remote-jobs/remotecorp-senior-frontend-developer</link>
  <description><![CDATA[
    <p>RemoteCorp is seeking a Senior Frontend Developer...</p>
    <p>Requirements: React, TypeScript, 5+ years experience</p>
    <p>Salary: $120,000 - $160,000</p>
  ]]></description>
  <pubDate>Mon, 22 Jul 2025 10:30:00 GMT</pubDate>
  <category>Programming</category>
</item>
```

### Output (to Database)
```json
{
  "id": "uuid-generated",
  "title": "Senior Frontend Developer",
  "company": "RemoteCorp", 
  "description": "",
  "source_url": "https://weworkremotely.com/remote-jobs/remotecorp-senior-frontend-developer",
  "apply_url": "https://weworkremotely.com/remote-jobs/remotecorp-senior-frontend-developer",
  "source_type": "wwr_rss",
  "source_name": "WeWorkRemotely",
  "processing_status": "pending",
  "created_at": "2025-07-22T10:30:00Z",
  "raw_sourced_job_data": {...}
}
```

## Performance Metrics

- **Runtime**: 5 seconds per run
- **Job Volume**: 80-100 jobs per run
- **Success Rate**: 99%+ (RSS feed is very stable)
- **Cost**: ~$0.001 per run
- **Frequency**: On-demand (manual trigger)
- **Reliability**: Highest among all sources

## Features

### Job Categories
WeWorkRemotely RSS includes jobs from various categories:
- **Programming**: Frontend, Backend, Full-stack
- **DevOps**: Infrastructure, Cloud, Security
- **Design**: UI/UX, Graphic, Product Design
- **Marketing**: Digital, Content, Growth
- **Sales**: Business Development, Account Management
- **Customer Success**: Support, Success Management

### Job Quality
- **100% Remote**: All jobs are remote-only positions
- **Vetted Companies**: Curated list of remote-friendly companies
- **Clear Requirements**: Well-structured job descriptions
- **Direct Apply**: Links directly to company application pages

## Troubleshooting

### Common Issues

#### 1. "No jobs found in RSS feed"
**Cause**: RSS feed temporarily unavailable or empty
**Solution**: 
- Check WeWorkRemotely.com status
- Wait 5-10 minutes and retry
- Verify RSS feed URL in actor configuration

#### 2. "Jobs not appearing in dashboard"
**Cause**: Actor completed but jobs not stored
**Diagnosis**:
- Check actor logs in Apify console
- Verify database connection
- Check for validation errors in logs

#### 3. "Duplicate jobs being created"
**Cause**: RSS items being processed multiple times
**Solution**: Check deduplication logic and external_id mapping

### Debugging Steps

1. **Check Actor Status**
```bash
curl -H "Authorization: Bearer $APIFY_TOKEN" \
  "https://api.apify.com/v2/acts/4q6w747EBIDBnPMG1/runs?limit=1"
```

2. **Check Database**
```sql
SELECT COUNT(*) FROM jobs WHERE source_name = 'WeWorkRemotely';
SELECT * FROM jobs WHERE source_name = 'WeWorkRemotely' ORDER BY created_at DESC LIMIT 5;
```

3. **Verify RSS Feed**
```bash
curl "https://weworkremotely.com/remote-jobs.rss" | head -50
```

## Monitoring

### Health Checks
- **RSS Feed Status**: Monitor feed availability and freshness
- **Actor Performance**: Track runtime and success rate
- **Data Quality**: Validate job count and field completeness
- **Deduplication**: Monitor for duplicate job entries

### Alerts
- RSS feed unavailable (> 10 minutes)
- Actor failures (any 4xx/5xx responses)
- Low job volume (< 70 jobs per run)
- Database insertion failures

## Maintenance

### Regular Tasks
- **Daily**: Monitor job volume and quality
- **Weekly**: Review new job categories and companies
- **Monthly**: Check actor performance and costs
- **Quarterly**: Update RSS parsing logic if needed

### Updates
- Actor version updates via Apify console
- RSS feed changes (rare but monitor WeWorkRemotely announcements)
- Field mapping updates in actor configuration

## Comparison with Other Sources

| Aspect           | WeWorkRemotely RSS | JobDataAPI      | Workable          |
| ---------------- | ------------------ | --------------- | ----------------- |
| **Speed**        | ⭐⭐⭐⭐⭐ (5s)         | ⭐⭐⭐⭐ (6s)       | ⭐⭐ (5min)         |
| **Volume**       | ⭐⭐⭐ (90 jobs)      | ⭐⭐⭐⭐ (100 jobs) | ⭐⭐⭐⭐⭐ (1000 jobs) |
| **Reliability**  | ⭐⭐⭐⭐⭐              | ⭐⭐⭐⭐            | ⭐⭐⭐⭐              |
| **Quality**      | ⭐⭐⭐⭐⭐              | ⭐⭐⭐⭐            | ⭐⭐⭐               |
| **Remote Focus** | ⭐⭐⭐⭐⭐ (100%)       | ⭐⭐⭐ (Mixed)     | ⭐⭐ (Mixed)        |

## API Reference

### Trigger Run
```http
POST /api/sources/wwr-rss/run
Content-Type: application/json

Response:
{
  "success": true,
  "message": "WeWorkRemotely RSS actor started successfully",
  "runId": "abc123..."
}
```

### Health Check
```http
GET /api/sources/wwr-rss/health
Content-Type: application/json

Response:
{
  "status": "healthy",
  "lastRun": "2025-07-22T10:30:00Z",
  "jobCount": 93,
  "avgRuntime": "5.2s"
}
```

## Data Schema

### RSS Item Structure
```xml
<item>
  <title>Job Title - Company Name</title>
  <link>https://weworkremotely.com/remote-jobs/...</link>
  <description><![CDATA[HTML job description]]></description>
  <pubDate>RFC 2822 date</pubDate>
  <category>Job Category</category>
  <guid>Unique identifier</guid>
</item>
```

### Extracted Fields
- **Title**: Parsed from `<title>` (format: "Job Title - Company Name")
- **Company**: Extracted from title after " - "
- **Description**: Raw HTML from `<description>`
- **URL**: Direct link from `<link>`
- **Category**: Job category from `<category>`
- **Published**: Date from `<pubDate>`

## Related Files

- **Trigger**: `/app/api/sources/wwr-rss/run/route.ts`
- **Health Check**: `/app/api/sources/wwr-rss/health/route.ts`
- **Webhook**: `/app/api/wwr-webhook/route.ts`
- **Constants**: `/lib/constants.ts`
- **Types**: `/lib/types.ts`

## Best Practices

1. **Frequency**: Run 2-3 times daily for optimal freshness
2. **Timing**: Run during business hours for best job availability
3. **Monitoring**: Check logs after each run for any parsing issues
4. **Deduplication**: Monitor for duplicate entries across runs

## Support

For issues with WeWorkRemotely RSS integration:
1. Check RSS feed availability first
2. Review actor logs in Apify console
3. Verify database connection and storage
4. Check field parsing and validation logic

## External Dependencies

- **WeWorkRemotely.com**: RSS feed provider
- **Apify Platform**: Actor execution environment
- **Supabase**: Database storage
- **Next.js API**: Integration endpoints 