# 🔔 Webhook Notification Solution

**Complete end-to-end visibility for your Bordfeed pipeline**

## 🎯 **Overview**

Your current setup has **Apify Slack notifications** for actor runs, but no visibility into webhook processing success/failure. This solution provides **3-layer notification coverage** for complete pipeline monitoring.

## 📋 **Current Status vs. Solution**

| **Component**           | **Current**           | **New Solution**                   |
| ----------------------- | --------------------- | ---------------------------------- |
| **Apify Actor Runs**    | ✅ Slack notifications | ✅ Keep existing setup              |
| **Webhook Processing**  | ❌ No notifications    | ✅ Automatic success/failure alerts |
| **QStash Processing**   | ❌ No callbacks        | ✅ Success & failure callbacks      |
| **Pipeline Visibility** | ⚠️ Partial logging     | ✅ End-to-end tracking              |

## 🏗️ **3-Layer Notification Architecture**

### **Layer 1: QStash Callbacks (Recommended)**
**✅ IMPLEMENTED** - QStash automatically sends success/failure callbacks

**How it works:**
```typescript
// Added to all webhook handlers:
headers: {
  'Upstash-Callback': 'https://bordfeed.com/api/webhook-callbacks',
  'Upstash-Failure-Callback': 'https://bordfeed.com/api/webhook-callbacks'
}
```

**Benefits:**
- ✅ **Automatic** - No manual triggers required
- ✅ **Reliable** - Built into QStash infrastructure  
- ✅ **Detailed** - Rich payload with timing, retries, costs
- ✅ **DLQ Support** - Dead Letter Queue notifications for failures

### **Layer 2: Enhanced Pipeline Logging (Active)**
**✅ ALREADY WORKING** - Your existing `logger.pipeline()` system

**Current tracking:**
```typescript
logger.pipeline({
  step: 'SOURCED',    // Jobs scraped from Apify
  step: 'DEDUPED',    // Duplicates filtered  
  step: 'QUEUED',     // Jobs queued to QStash
  step: 'PROCESSED',  // AI extraction completed
  step: 'STORED',     // Jobs saved to database
})
```

### **Layer 3: Slack Integration (Active)**
**✅ ALREADY WORKING** - Your existing Slack webhook integration

**Alert Levels:**
- `logger.critical()` - System failures, data corruption
- `logger.alert()` - High failure rates, performance issues  
- `logger.notify()` - Successful milestones, metrics

## 🔄 **Complete Flow Diagram**

```mermaid
graph TD
    A[Apify Actor Completes] -->|✅ Slack notification| B[Webhook Triggered]
    B --> C[Deduplication]
    C --> D[Queue via QStash]
    D -->|✅ Callback URLs added| E[Pipeline Processing]
    E -->|Success| F[✅ Success Callback]
    E -->|Failure| G[❌ Failure Callback] 
    F --> H[✅ Slack Success Alert]
    G --> I[🚨 Slack Critical Alert]
    
    subgraph "New Callback Endpoint"
        F --> J[/api/webhook-callbacks]
        G --> J
        J --> K[Parse QStash Response]
        K --> L[Extract Metrics]
        L --> M[Send Slack Notification]
    end
```

## 📊 **What You'll Get**

### **Success Notifications**
```
🎉 Pipeline Processing Completed
• Batch: jobdata-12345-batch-1
• Jobs Processed: 25 → 23 saved, 2 failed
• Duplicates Skipped: 3 (cost saved: $0.006)
• Processing Time: 2.3s
• QStash Retries: 0
• Total Cost: $0.046
```

### **Failure Notifications**  
```
💥 QStash Processing Failed - DLQ Alert
• Batch: wwr-67890-batch-2  
• Jobs Lost: 15
• Final Status: 500 Internal Server Error
• Retry Attempts: 3/3
• DLQ ID: dlq_abc123
• Action: Check https://console.upstash.com/qstash/dlq/dlq_abc123
```

### **High Failure Rate Alerts**
```
⚠️ High Failure Rate Detected
• Batch: workable-11111-batch-3
• Failure Rate: 65% (13 failed / 20 total)
• Successful: 7 jobs
• Action: Check AI processing or data quality
```

## 🚀 **Implementation Status**

### ✅ **Completed**
1. **QStash Callback Endpoint** - `/api/webhook-callbacks`
2. **Updated Webhook Handlers** - All 3 sources now include callbacks
3. **Enhanced Logging** - Comprehensive pipeline tracking
4. **Slack Integration** - Multi-level alerting system

### 🔧 **Files Modified**
- `app/api/webhook-callbacks/route.ts` - **NEW** callback handler
- `app/api/jobdata-webhook/route.ts` - Added QStash callbacks  
- `app/api/workable-webhook/route.ts` - Added QStash callbacks
- `app/api/wwr-webhook/route.ts` - Added QStash callbacks

## 📈 **Notification Sources Comparison**

| **Source**     | **Best For**               | **Our Choice**             |
| -------------- | -------------------------- | -------------------------- |
| **Apify**      | Actor run notifications    | ✅ Keep existing            |
| **QStash**     | Processing success/failure | ✅ **RECOMMENDED**          |
| **Our System** | Custom business logic      | ✅ Enhanced logging         |
| **Upstash**    | Infrastructure monitoring  | ⚠️ Limited webhook features |

## 🎛️ **Testing Your Setup**

### **Test QStash Callbacks**
```bash
# Trigger a test job processing run
curl -X POST https://bordfeed.com/api/sources/jobdata-api/run

# Check Slack for notifications:
# 1. Apify actor completion (existing)
# 2. Webhook processing steps (enhanced)  
# 3. QStash callback results (new)
```

### **Test Failure Scenarios**
```bash
# Simulate a processing failure
curl -X POST https://bordfeed.com/api/webhook-callbacks \
  -H "Content-Type: application/json" \
  -d '{"status": 500, "dlqId": "test_dlq_123", "sourceMessageId": "test_msg"}'
```

## 📱 **Slack Channel Setup**

**Recommended Slack workflow:**
1. **#bordfeed-alerts** - Critical/Alert notifications
2. **#bordfeed-pipeline** - Pipeline step tracking  
3. **#bordfeed-success** - Success notifications & metrics

## 🔍 **Monitoring Dashboard**

With this setup, you'll have visibility into:

- ✅ **Actor Success Rate** (from Apify)
- ✅ **Webhook Processing Rate** (from callbacks)  
- ✅ **Job Processing Success** (from pipeline logs)
- ✅ **Cost Tracking** (from AI processing)
- ✅ **Performance Metrics** (from timing data)

## 🚨 **Alerting Strategy**

### **Critical Alerts** (Immediate Action)
- QStash message sent to DLQ
- Database connection failures
- All jobs failing in a batch

### **Warning Alerts** (Monitor)  
- High failure rates (>50%)
- Slow processing (>5min per batch)
- High duplicate rates (>80%)

### **Info Notifications** (Metrics)
- Successful batch completions
- Cost savings from deduplication  
- Daily/weekly summaries

## 🎉 **Next Steps**

1. **Deploy the changes** (already implemented)
2. **Test with a manual actor run**
3. **Monitor Slack for new callback notifications**
4. **Adjust alert thresholds** if needed

Your notification system is now **complete end-to-end**! 🚀 