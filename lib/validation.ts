import { z } from 'zod';
import { INPUT_LIMITS } from './constants';

// Schema for extract API request
export const ExtractRequestSchema = z.object({
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
  sourceUrl: z.string().url().optional(),
});

// Extract job request schema - updated to support updating existing jobs
export const ExtractJobRequestSchema = z.object({
  jobId: z.string().uuid().optional(), // If provided, update existing job
  sourcedAt: z.string().datetime().optional(),
  sourceUrl: z.string().url().optional(),
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
});

export type ExtractJobRequest = z.infer<typeof ExtractJobRequestSchema>;
