import { z } from 'zod';
import { APPLY_METHODS } from './apply-methods';
import { CAREER_LEVELS } from './career-levels';
import { SCHEMA_LIMITS } from './constants';
import { countries } from './data/countries';
import { CURRENCY_CODES } from './data/currencies';
import { LANGUAGE_CODES } from './data/languages';
import { JOB_STATUSES, VISA_SPONSORSHIP_OPTIONS } from './job-status';
import { JOB_TYPES } from './job-types';
import { SALARY_UNITS } from './salary-units';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from './workplace';

// Simplified schema for AI extraction
export const JobExtractionSchema = z.object({
  // Core fields
  title: z.string(),
  company: z.string().nullable().optional(),
  type: z.enum(JOB_TYPES).nullable().optional(),
  description: z.string(),
  apply_url: z.string().nullable().optional(),
  apply_method: z.enum(APPLY_METHODS).nullable().optional(),
  posted_date: z.string().datetime().nullable().optional(),
  status: z.enum(JOB_STATUSES).default('active'),

  // Salary
  salary_min: z.number().int().positive().nullable().optional(),
  salary_max: z.number().int().positive().nullable().optional(),
  salary_currency: z.enum(CURRENCY_CODES).nullable().optional(),
  salary_unit: z.enum(SALARY_UNITS).nullable().optional(),

  // Location & Remote work
  workplace_type: z.enum(WORKPLACE_TYPES).nullable().optional(),
  remote_region: z.enum(REMOTE_REGIONS).nullable().optional(),
  timezone_requirements: z.string().nullable().optional(),
  workplace_city: z.string().nullable().optional(),
  workplace_country: z.enum(countries).nullable().optional(),

  // Additional details
  benefits: z.string().max(SCHEMA_LIMITS.benefits).nullable().optional(),
  application_requirements: z
    .string()
    .max(SCHEMA_LIMITS.application_requirements)
    .nullable()
    .optional(),
  valid_through: z.string().datetime().nullable().optional(),
  job_identifier: z.string().nullable().optional(),
  job_source_name: z.string().nullable().optional(),
  department: z.string().nullable().optional(),
  travel_required: z.boolean().nullable().optional(),

  // Career & Skills
  career_level: z.array(z.enum(CAREER_LEVELS)).nullable().optional(),
  visa_sponsorship: z.enum(VISA_SPONSORSHIP_OPTIONS).nullable().optional(),
  languages: z.array(z.enum(LANGUAGE_CODES)).nullable().optional(),
  skills: z.string().max(SCHEMA_LIMITS.skills).nullable().optional(),
  qualifications: z
    .string()
    .max(SCHEMA_LIMITS.qualifications)
    .nullable()
    .optional(),
  education_requirements: z
    .string()
    .max(SCHEMA_LIMITS.education_requirements)
    .nullable()
    .optional(),
  experience_requirements: z
    .string()
    .max(SCHEMA_LIMITS.experience_requirements)
    .nullable()
    .optional(),
  responsibilities: z
    .string()
    .max(SCHEMA_LIMITS.responsibilities)
    .nullable()
    .optional(),

  // SEO & Classification
  featured: z.boolean().nullable().optional(),
  industry: z.string().nullable().optional(),
  occupational_category: z.string().nullable().optional(),
});

// Full schema including metadata
export const JobSchema = z.object({
  sourcedAt: z.string().datetime(),
  sourceUrl: z.string().url(),
  ...JobExtractionSchema.shape,
});

export type Job = z.infer<typeof JobSchema>;
export type JobExtraction = z.infer<typeof JobExtractionSchema>;
