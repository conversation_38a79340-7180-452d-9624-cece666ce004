// Job Board Service - Database-driven configuration
// Replaces hardcoded configs for scalability

import type { JobBoardTimezone, PostingStrategy } from './job-board-constants';
import { createServerClient } from './supabase';
import type { JobFilters } from './tagging-engine';
import type { JobData } from './types';
import { logger } from './utils';

export interface JobBoardConfig {
  id: string;
  name: string;
  description: string;
  enabled: boolean;

  // Airtable settings
  airtable: {
    baseId: string;
    tableName: string;
    pat?: string;
  };

  // Posting settings
  posting: {
    dailyLimit: number;
    strategy: PostingStrategy;
    avoidRepostingDays: number;
    postingTimes?: string[];
    timezone?: JobBoardTimezone;
  };

  // Filters
  filters: {
    types?: string[];
    workplaceTypes?: string[];
    remoteRegions?: string[];
    countries?: string[];
    careerLevels?: string[];
    salaryMin?: number;
    salaryMax?: number;
    salaryCurrencies?: string[];
    includeKeywords?: string[];
    excludeKeywords?: string[];
    visaSponsorship?: string;
    languages?: string[];
    sourcePriority?: string[];
  };

  // Tracking
  lastPostedAt?: string;
  totalPosted?: number;
}

interface DatabaseJobBoardConfig {
  id: string;
  name: string;
  description: string | null;
  enabled: boolean;
  airtable_base_id: string;
  airtable_table_name: string;
  airtable_pat: string | null;
  daily_limit: number;
  posting_strategy: string;
  avoid_reposting_days: number;
  posting_times: string[] | null;
  timezone: string | null;
  // biome-ignore lint/suspicious/noExplicitAny: Filter values can be any type (string, number, array, etc.)
  filters: Record<string, any>;
  last_posted_at: string | null;
  total_posted: number;
}

/**
 * Load all job board configurations from database
 */
export async function getJobBoardConfigs(): Promise<JobBoardConfig[]> {
  const supabase = await createServerClient();

  const { data: configs, error } = await supabase
    .from('job_board_configs')
    .select('*')
    .order('created_at');

  if (error) {
    logger.error('Error loading job board configs:', error);
    return [];
  }

  return (configs || []).map(transformDatabaseConfig);
}

/**
 * Get only enabled job board configurations
 */
export async function getActiveJobBoards(): Promise<JobBoardConfig[]> {
  const supabase = await createServerClient();

  const { data: configs, error } = await supabase
    .from('job_board_configs')
    .select('*')
    .eq('enabled', true)
    .order('created_at');

  if (error) {
    logger.error('Error loading active job board configs:', error);
    return [];
  }

  return (configs || []).map(transformDatabaseConfig);
}

/**
 * Get a specific job board configuration
 */
export async function getJobBoardById(
  id: string
): Promise<JobBoardConfig | null> {
  const supabase = await createServerClient();

  const { data: config, error } = await supabase
    .from('job_board_configs')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    logger.error(`Error loading job board config ${id}:`, error);
    return null;
  }

  return config ? transformDatabaseConfig(config) : null;
}

/**
 * Create a new job board configuration
 */
export async function createJobBoard(
  config: Omit<JobBoardConfig, 'lastPostedAt' | 'totalPosted'>
): Promise<boolean> {
  const supabase = await createServerClient();

  const dbConfig = {
    id: config.id,
    name: config.name,
    description: config.description,
    enabled: config.enabled,
    airtable_base_id: config.airtable.baseId,
    airtable_table_name: config.airtable.tableName,
    airtable_pat: config.airtable.pat,
    daily_limit: config.posting.dailyLimit,
    posting_strategy: config.posting.strategy,
    avoid_reposting_days: config.posting.avoidRepostingDays,
    posting_times: config.posting.postingTimes,
    timezone: config.posting.timezone,
    filters: config.filters,
  };

  const { error } = await supabase.from('job_board_configs').insert(dbConfig);

  if (error) {
    logger.error('Error creating job board config:', error);
    return false;
  }

  return true;
}

/**
 * Update an existing job board configuration
 */
export async function updateJobBoard(
  id: string,
  updates: Partial<JobBoardConfig>
): Promise<boolean> {
  const supabase = await createServerClient();

  // Convert updates to database format
  const dbUpdates: Partial<DatabaseJobBoardConfig> = {};

  if (updates.name) {
    dbUpdates.name = updates.name;
  }
  if (updates.description !== undefined) {
    dbUpdates.description = updates.description;
  }
  if (updates.enabled !== undefined) {
    dbUpdates.enabled = updates.enabled;
  }
  if (updates.airtable?.baseId) {
    dbUpdates.airtable_base_id = updates.airtable.baseId;
  }
  if (updates.airtable?.tableName) {
    dbUpdates.airtable_table_name = updates.airtable.tableName;
  }
  if (updates.airtable?.pat !== undefined) {
    dbUpdates.airtable_pat = updates.airtable.pat;
  }
  if (updates.posting?.dailyLimit) {
    dbUpdates.daily_limit = updates.posting.dailyLimit;
  }
  if (updates.posting?.strategy) {
    dbUpdates.posting_strategy = updates.posting.strategy;
  }
  if (updates.posting?.avoidRepostingDays) {
    dbUpdates.avoid_reposting_days = updates.posting.avoidRepostingDays;
  }
  if (updates.posting?.postingTimes !== undefined) {
    dbUpdates.posting_times = updates.posting.postingTimes;
  }
  if (updates.posting?.timezone !== undefined) {
    dbUpdates.timezone = updates.posting.timezone;
  }
  if (updates.filters) {
    dbUpdates.filters = updates.filters;
  }

  const { error } = await supabase
    .from('job_board_configs')
    .update(dbUpdates)
    .eq('id', id);

  if (error) {
    logger.error(`Error updating job board config ${id}:`, error);
    return false;
  }

  return true;
}

/**
 * Delete a job board configuration
 */
export async function deleteJobBoard(id: string): Promise<boolean> {
  const supabase = await createServerClient();

  const { error } = await supabase
    .from('job_board_configs')
    .delete()
    .eq('id', id);

  if (error) {
    logger.error(`Error deleting job board config ${id}:`, error);
    return false;
  }

  return true;
}

/**
 * Update posting stats for a job board
 */
export async function updateJobBoardStats(
  id: string,
  increment = 1
): Promise<boolean> {
  const supabase = await createServerClient();

  const { error } = await supabase
    .from('job_board_configs')
    .update({
      total_posted: supabase.rpc('increment', {
        field: 'total_posted',
        amount: increment,
      }),
      last_posted_at: new Date().toISOString(),
    })
    .eq('id', id);

  if (error) {
    logger.error(`Error updating job board stats ${id}:`, error);
    return false;
  }

  return true;
}

/**
 * Transform database config to application format
 */
function transformDatabaseConfig(
  dbConfig: DatabaseJobBoardConfig
): JobBoardConfig {
  return {
    id: dbConfig.id,
    name: dbConfig.name,
    description: dbConfig.description || '',
    enabled: dbConfig.enabled,
    airtable: {
      baseId: dbConfig.airtable_base_id,
      tableName: dbConfig.airtable_table_name,
      pat: dbConfig.airtable_pat || undefined,
    },
    posting: {
      dailyLimit: dbConfig.daily_limit,
      strategy: dbConfig.posting_strategy as PostingStrategy,
      avoidRepostingDays: dbConfig.avoid_reposting_days,
      postingTimes: dbConfig.posting_times || undefined,
      timezone: (dbConfig.timezone as JobBoardTimezone) || undefined,
    },
    filters: dbConfig.filters || {},
    lastPostedAt: dbConfig.last_posted_at || undefined,
    totalPosted: dbConfig.total_posted,
  };
}

/**
 * Get matching jobs for a board with proper filtering
 */
export async function getMatchingJobs(
  boardId: string,
  limit = 50
): Promise<JobData[]> {
  const supabase = await createServerClient();

  // Get board configuration
  const board = await getJobBoardById(boardId);
  if (!board) {
    throw new Error(`Job board ${boardId} not found`);
  }

  // Convert board filters to our new filter format
  const _filters: JobFilters = {
    careerLevels: board.filters.careerLevels,
    workplaceTypes: board.filters.workplaceTypes,
    remoteRegions: board.filters.remoteRegions,
    jobTypes: board.filters.types,
    salaryMin: board.filters.salaryMin,
    salaryMax: board.filters.salaryMax,
    salaryCurrency: board.filters.salaryCurrencies?.[0],
    includeKeywords: board.filters.includeKeywords,
    excludeKeywords: board.filters.excludeKeywords,
    countries: board.filters.countries,
  };

  // First, get all posted job IDs for this board
  const { data: postedJobs } = await supabase
    .from('job_board_postings')
    .select('job_id')
    .eq('board_id', boardId);

  const postedJobIds = new Set(postedJobs?.map((p) => p.job_id) || []);

  // Get all active jobs
  const { data: allJobs, error } = await supabase
    .from('jobs')
    .select('*')
    .eq('status', 'active')
    .eq('processing_status', 'completed')
    .not('source_url', 'is', null)
    .order('created_at', { ascending: false })
    .limit(limit * 3); // Get extra to account for filtering

  if (error) {
    throw new Error(`Database error: ${error.message}`);
  }

  if (!allJobs) {
    return [];
  }

  // Filter out already posted jobs and apply filters
  const eligibleJobs = allJobs
    .filter((job) => !postedJobIds.has(job.id))
    .filter((job) => matchesJobBoardNew(job, board))
    .slice(0, limit);

  return eligibleJobs as JobData[];
}

/**
 * Check basic string/array filters
 */
function checkBasicFilters(
  job: JobData,
  filters: JobBoardConfig['filters']
): boolean {
  if (filters.types?.length && job.type && !filters.types.includes(job.type)) {
    return false;
  }

  if (
    filters.workplaceTypes?.length &&
    job.workplace_type &&
    !filters.workplaceTypes.includes(job.workplace_type)
  ) {
    return false;
  }

  if (
    filters.remoteRegions?.length &&
    job.remote_region &&
    !filters.remoteRegions.includes(job.remote_region)
  ) {
    return false;
  }

  if (
    filters.countries?.length &&
    job.workplace_country &&
    !filters.countries.includes(job.workplace_country)
  ) {
    return false;
  }

  return true;
}

/**
 * Check array-based filters like career level and languages
 */
function checkArrayFilters(
  job: JobData,
  filters: JobBoardConfig['filters']
): boolean {
  if (filters.careerLevels?.length && job.career_level) {
    const hasMatchingLevel = job.career_level.some((level: string) =>
      filters.careerLevels?.includes(level)
    );
    if (!hasMatchingLevel) {
      return false;
    }
  }

  if (filters.languages?.length && job.languages) {
    const hasMatchingLang = job.languages.some((lang: string) =>
      filters.languages?.includes(lang)
    );
    if (!hasMatchingLang) {
      return false;
    }
  }

  return true;
}

/**
 * Check salary-related filters
 */
function checkSalaryFilters(
  job: JobData,
  filters: JobBoardConfig['filters']
): boolean {
  if (
    filters.salaryMin &&
    job.salary_max &&
    job.salary_max < filters.salaryMin
  ) {
    return false;
  }

  if (
    filters.salaryMax &&
    job.salary_min &&
    job.salary_min > filters.salaryMax
  ) {
    return false;
  }

  if (
    filters.salaryCurrencies?.length &&
    job.salary_currency &&
    !filters.salaryCurrencies.includes(job.salary_currency)
  ) {
    return false;
  }

  return true;
}

/**
 * Check keyword-based text filters
 */
function checkKeywordFilters(
  job: JobData,
  filters: JobBoardConfig['filters']
): boolean {
  const jobText = `${job.title || ''} ${job.description || ''} ${
    job.skills || ''
  }`.toLowerCase();

  if (filters.includeKeywords?.length) {
    const hasKeyword = filters.includeKeywords.some((keyword) =>
      jobText.includes(keyword.toLowerCase())
    );
    if (!hasKeyword) {
      return false;
    }
  }

  if (filters.excludeKeywords?.length) {
    const hasExcluded = filters.excludeKeywords.some((keyword) =>
      jobText.includes(keyword.toLowerCase())
    );
    if (hasExcluded) {
      return false;
    }
  }

  return true;
}

/**
 * Updated job matching function using consistent filter logic
 */
export function matchesJobBoardNew(
  job: JobData,
  board: JobBoardConfig
): boolean {
  const { filters } = board;

  // Run all filter checks
  if (!checkBasicFilters(job, filters)) {
    return false;
  }
  if (!checkArrayFilters(job, filters)) {
    return false;
  }
  if (!checkSalaryFilters(job, filters)) {
    return false;
  }
  if (!checkKeywordFilters(job, filters)) {
    return false;
  }

  // Visa sponsorship filter
  if (
    filters.visaSponsorship &&
    job.visa_sponsorship !== filters.visaSponsorship
  ) {
    return false;
  }

  return true;
}
