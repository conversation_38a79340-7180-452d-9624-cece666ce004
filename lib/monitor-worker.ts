import type { ClassificationResult } from './monitor';
import {
  classifyStatus,
  downloadSnippet,
  fetchHead,
  matchClosedPhrase,
} from './monitor';
import { createServerClient } from './supabase';
import { generateMetadata, logger } from './utils';

export interface JobRow {
  id: string;
  status: string | null;
  last_checked_at: string | null;
  monitor_attempts: number;
  next_try_at: string | null;
  source_url: string;
  description: string;
}

export interface SelectJobsOptions {
  limit?: number;
  lockMinutes?: number;
}

export interface PipelineResult extends ClassificationResult {
  jobId: string;
  headStatus: number;
  headOk: boolean;
  /** Which layer decided the final status: head | phrase | ai */
  decisionLayer: 'head' | 'phrase' | 'ai';
  error?: string;
}

// Type for metadata that includes the model property
type MetadataWithModel = ReturnType<typeof generateMetadata> & {
  model?: string;
};

/**
 * Select a batch of jobs ready for monitoring and lock them by setting `next_try_at`.
 * NOTE: Uses a best-effort approach; for strict concurrency, move to an SQL function.
 */
export async function selectJobsForMonitoring({
  limit = 20,
  lockMinutes = 10,
}: SelectJobsOptions = {}): Promise<JobRow[]> {
  const supabase = await createServerClient();

  const nowIso = new Date().toISOString();

  const { data: jobs, error } = await supabase
    .from('jobs')
    .select('*')
    .or('status.eq.active,status.eq.unknown,status.is.null')
    .or(`next_try_at.lte.${nowIso},next_try_at.is.null`)
    .order('monitor_attempts', { ascending: true })
    .order('sourced_at', { ascending: true })
    .limit(limit);

  if (error) {
    throw error;
  }

  if (!jobs || jobs.length === 0) {
    return [];
  }

  const lockUntil = new Date(Date.now() + lockMinutes * 60_000).toISOString();
  const jobIds = jobs.map((j) => j.id);

  const { error: updateError } = await supabase
    .from('jobs')
    .update({
      monitor_attempts: undefined, // will increment separately
      next_try_at: lockUntil,
      last_checked_at: nowIso,
    })
    .in('id', jobIds);

  if (updateError) {
    logger.error('Failed to lock jobs for monitoring', updateError);
  }

  // Increment monitor_attempts individually
  await Promise.all(
    jobs.map((job) =>
      supabase
        .from('jobs')
        .update({
          monitor_attempts: (job.monitor_attempts || 0) + 1,
        })
        .eq('id', job.id)
    )
  );

  return jobs as unknown as JobRow[];
}

/**
 * Process a single job through the monitoring pipeline (A→B→C).
 */
export async function monitorJob(job: JobRow): Promise<PipelineResult> {
  const startTime = Date.now();
  try {
    // Step A: HEAD request
    const head = await fetchHead(job.source_url).catch((_e) => null);
    if (!head || head.status >= 400) {
      return {
        jobId: job.id,
        headStatus: head?.status || 0,
        headOk: false,
        status: 'closed',
        confidence: 0.95,
        metadata: generateMetadata(Date.now(), undefined, {
          model: 'heuristic',
        }),
        decisionLayer: 'head',
      } satisfies PipelineResult;
    }

    // Step B: download HTML snippet
    const snippetRes = await downloadSnippet(job.source_url);

    // Step C: Quick heuristic -> phrase matcher
    const phrasesModule = await import('./data/closed-phrases.json', {
      with: { type: 'json' },
    });
    const phrases = phrasesModule.default as readonly string[];
    const pm = matchClosedPhrase(snippetRes.content, phrases);

    if (pm.matched) {
      return {
        jobId: job.id,
        headStatus: head.status,
        headOk: true,
        status: 'closed',
        confidence: 0.9,
        metadata: generateMetadata(startTime, undefined, {
          model: 'heuristic',
          matchedPhrase: pm.phrase,
        }),
        decisionLayer: 'phrase',
      };
    }

    // Step D: classify status with AI
    const classification = await classifyStatus(snippetRes.content);

    return {
      jobId: job.id,
      headStatus: head.status,
      headOk: true,
      decisionLayer: 'ai',
      ...classification,
    } satisfies PipelineResult;
  } catch (error) {
    logger.error('monitorJob error', { jobId: job.id, error });
    return {
      jobId: job.id,
      headStatus: 0,
      headOk: false,
      decisionLayer: 'head',
      status: 'unknown',
      confidence: 0,
      metadata: generateMetadata(Date.now(), undefined, { model: 'error' }),
      error: error instanceof Error ? error.message : String(error),
    } satisfies PipelineResult;
  }
}

/**
 * Monitor a batch of jobs concurrently.
 */
export function monitorBatch(jobs: JobRow[]): Promise<PipelineResult[]> {
  return Promise.all(jobs.map((j) => monitorJob(j)));
}

/**
 * Persist pipeline results: insert into job_monitor_logs and update jobs table.
 */
export async function persistMonitorResults(
  results: PipelineResult[]
): Promise<void> {
  if (results.length === 0) {
    return;
  }

  const supabase = await createServerClient();

  // Insert logs
  const logsPayload = results.map((r) => ({
    job_id: r.jobId,
    previous_status: null, // filled later if needed
    new_status: r.status,
    checked_at: r.metadata.timestamp,
    duration_millis: r.metadata.duration,
    cost_input_tokens: r.metadata.usage.inputTokens,
    cost_output_tokens: r.metadata.usage.outputTokens,
    total_tokens: r.metadata.usage.totalTokens,
    model: (r.metadata as MetadataWithModel).model ?? null,
    head_status: r.headStatus,
    head_ok: r.headOk,
    decision_layer: r.decisionLayer,
  }));

  const { error: logErr } = await supabase
    .from('job_monitor_logs')
    .insert(logsPayload);
  if (logErr) {
    logger.error('Failed to insert monitor logs', logErr);
  }

  // Update jobs table with new status and increment updated_at
  await Promise.all(
    results.map((r) => {
      const now = Date.now();
      let nextTry: string | null = null;

      if (!r.headOk) {
        // Network error → retry in 6 hours
        nextTry = new Date(now + 6 * 60 * 60 * 1000).toISOString();
      } else if (r.decisionLayer === 'head' && r.status === 'closed') {
        // Closed decision came solely from HEAD; schedule fast confirmation in 30 min
        nextTry = new Date(now + 30 * 60 * 1000).toISOString();
      } else if (r.status === 'unknown') {
        // AI wasn't sure → retry in 24 hours
        nextTry = new Date(now + 24 * 60 * 60 * 1000).toISOString();
      }

      return supabase
        .from('jobs')
        .update({
          status: r.status,
          updated_at: new Date().toISOString(),
          next_try_at: nextTry,
        })
        .eq('id', r.jobId);
    })
  );
}
