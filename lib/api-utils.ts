import { NextResponse } from 'next/server';
import type { ZodError } from 'zod';
import { DEBUG_MODE } from './constants';
import { logger } from './utils';

/**
 * Standard error response for validation failures
 */
export function validationErrorResponse(error: ZodError) {
  return NextResponse.json(
    {
      error: 'Invalid request body',
      details: error.flatten().fieldErrors,
    },
    { status: 400 }
  );
}

// Type for error cause object
interface ErrorCause {
  issues?: unknown[];
  text?: string;
  value?: unknown;
  [key: string]: unknown;
}

/**
 * Process AI response text from error cause
 */
function processAiResponseText(text: string): {
  aiResponse?: unknown;
  aiResponseRaw?: string;
} {
  try {
    const parsed = JSON.parse(text);
    if (DEBUG_MODE) {
      logger.log('Parsed AI response:', parsed);
    }
    return { aiResponse: parsed };
  } catch {
    if (DEBUG_MODE) {
      logger.log('Raw AI response (failed to parse):', text);
    }
    return { aiResponseRaw: text };
  }
}

/**
 * Extract validation details from error cause
 */
function extractCauseDetails(cause: ErrorCause): Record<string, unknown> {
  const details: Record<string, unknown> = {};

  if ('issues' in cause) {
    details.validationErrors = cause.issues;
  }

  if ('text' in cause && cause.text) {
    Object.assign(details, processAiResponseText(cause.text));
  }

  if ('value' in cause) {
    details.parsedValue = cause.value;
    if (DEBUG_MODE) {
      logger.log('Parsed value from cause:', cause.value);
    }
  }

  return details;
}

/**
 * Extract and format schema mismatch error details
 */
export function extractSchemaErrorDetails(
  error: Error,
  contextName = 'Schema'
) {
  const errorDetails: Record<string, unknown> = {
    error: 'Schema validation failed',
    message: `The AI response did not match the expected ${contextName.toLowerCase()} schema`,
    detail: error.message,
  };

  // Extract validation issues if available
  if ('cause' in error && error.cause && typeof error.cause === 'object') {
    const cause = error.cause as ErrorCause;
    Object.assign(errorDetails, extractCauseDetails(cause));
  }

  if (DEBUG_MODE) {
    logger.log(`${contextName} validation error details:`, errorDetails);
  }

  return errorDetails;
}

/**
 * Standard error handler for API routes
 */
export function handleApiError(error: unknown, contextName = 'Operation') {
  // Handle schema mismatch errors
  if (
    error instanceof Error &&
    error.message.includes('response did not match schema')
  ) {
    const errorDetails = extractSchemaErrorDetails(error, contextName);
    return NextResponse.json(errorDetails, { status: 422 });
  }

  // Handle generic errors
  return NextResponse.json(
    {
      error: 'Internal server error',
      detail: error instanceof Error ? error.message : 'Unknown error',
    },
    { status: 500 }
  );
}

/**
 * Replace template placeholders in a string
 */
export function replaceTemplateVariables(
  template: string,
  variables: Record<string, string>
): string {
  let result = template;
  for (const [key, value] of Object.entries(variables)) {
    result = result.replaceAll(`{${key}}`, value);
  }
  return result;
}
