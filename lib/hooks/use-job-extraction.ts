'use client';

import { useState } from 'react';
import type {
  ApiError,
  ExtractResponse,
  JobData,
  LoadingState,
} from '../types';
import { logger } from '../utils';

export function useJobExtraction() {
  const [result, setResult] = useState<JobData | null>(null);
  const [loading, setLoading] = useState<LoadingState>('idle');
  const [error, setError] = useState<string>('');
  const [metadata, setMetadata] = useState<ExtractResponse['metadata'] | null>(
    null
  );

  const extractJob = async (content: string, jobId?: string) => {
    if (!content.trim()) {
      setError('Please enter job post content');
      return;
    }

    setLoading('loading');
    setError('');
    setResult(null);
    setMetadata(null);

    try {
      const requestBody: { content: string; jobId?: string } = { content };
      if (jobId) {
        requestBody.jobId = jobId;
      }

      const response = await fetch('/api/extract', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        const apiError = data as ApiError;
        if (apiError.parsedValue) {
          logger.error(
            'Parsed value that failed validation:',
            apiError.parsedValue
          );
        }
        throw new Error(apiError.error || 'Extraction failed');
      }

      const extractResponse = data as ExtractResponse;
      setResult(extractResponse.job);
      setMetadata(extractResponse.metadata);
      setLoading('success');
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading('error');
    }
  };

  const clearResults = () => {
    setResult(null);
    setError('');
    setMetadata(null);
    setLoading('idle');
  };

  return {
    result,
    loading,
    error,
    metadata,
    extractJob,
    clearResults,
  };
}
