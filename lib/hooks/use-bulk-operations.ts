import { useCallback } from "react";
import type { DatabaseJob } from "@/lib/storage";

interface BulkOperationsProps {
  selectedRows: { original: DatabaseJob }[];
  onRefresh: () => Promise<void>;
  onClearSelection: () => void;
}

export function useBulkOperations({
  selectedRows,
  onRefresh,
  onClearSelection,
}: BulkOperationsProps) {
  const handleBulkDelete = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Delete ${selectedRows.length} selected job(s)? This cannot be undone.`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch("/api/jobs", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ids: jobIds }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete jobs");
      }

      await onRefresh();
      onClearSelection();
    } catch (_error) {
      alert("Failed to delete jobs");
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkStatusUpdate = useCallback(
    async (newStatus: string) => {
      if (selectedRows.length === 0) {
        return;
      }

      const confirmed = window.confirm(
        `Update status to "${newStatus}" for ${selectedRows.length} selected job(s)?`
      );
      if (!confirmed) {
        return;
      }

      try {
        const jobIds = selectedRows.map((row) => row.original.id);
        const response = await fetch("/api/jobs", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            action: "bulkUpdateStatus",
            ids: jobIds,
            status: newStatus,
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to update job status");
        }

        await onRefresh();
        onClearSelection();
      } catch (_error) {
        alert("Failed to update job status");
      }
    },
    [selectedRows, onRefresh, onClearSelection]
  );

  const handleBulkRequeueAI = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Requeue AI processing for ${selectedRows.length} selected job(s)?`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch("/api/jobs", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "bulkRequeueExtraction",
          ids: jobIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to requeue AI processing");
      }

      await onRefresh();
      onClearSelection();
    } catch (_error) {
      alert("Failed to requeue AI processing");
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkAirtablePush = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Push ${selectedRows.length} selected job(s) to Airtable?`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch("/api/jobs", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "bulkPushAirtable",
          ids: jobIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to push to Airtable");
      }

      await onRefresh();
      onClearSelection();
    } catch (_error) {
      alert("Failed to push to Airtable");
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkResetMonitor = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Reset monitor status for ${selectedRows.length} selected job(s)?`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch("/api/jobs", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "bulkResetMonitor",
          ids: jobIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to reset monitor");
      }

      await onRefresh();
      onClearSelection();
    } catch (_error) {
      alert("Failed to reset monitor");
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkProcessNow = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Process ${selectedRows.length} selected job(s) now? This will trigger AI extraction for all selected jobs.`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch("/api/jobs", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "bulkProcessNow",
          ids: jobIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to process jobs");
      }

      const result = await response.json();

      // Show detailed feedback about the processing results
      if (result.stats) {
        const { total, successful, failed } = result.stats;
        let message = `Processing completed!\n\nTotal: ${total}\nSuccessful: ${successful}`;
        if (failed > 0) {
          message += `\nFailed: ${failed}`;
        }
        alert(message);
      } else {
        alert("Jobs processing started successfully!");
      }

      await onRefresh();
      onClearSelection();
    } catch (_error) {
      alert("Failed to process jobs");
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  return {
    handleBulkDelete,
    handleBulkStatusUpdate,
    handleBulkRequeueAI,
    handleBulkAirtablePush,
    handleBulkResetMonitor,
    handleBulkProcessNow,
  };
}
