import { useCallback, useEffect, useState } from 'react';
import type { DatabaseJob } from '../storage';
import { logger } from '../utils';

interface JobFilters {
  search: string;
  status: string;
  processingStatus: string;
  sourceType: string;
  workplaceType: string;
  dateRange: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface SortingInfo {
  field: string;
  direction: 'asc' | 'desc';
}

interface JobsListState {
  jobs: DatabaseJob[];
  loading: boolean;
  error: string;
  pagination: PaginationInfo;
  filters: JobFilters;
  sorting: SortingInfo;
}

interface JobsListActions {
  loadJobs: () => Promise<void>;
  setFilter: (key: keyof JobFilters, value: string) => void;
  setSort: (field: string) => void;
  setPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  refreshJobs: () => Promise<void>;
  clearError: () => void;
}

const initialFilters: JobFilters = {
  search: '',
  status: '',
  processingStatus: '',
  sourceType: '',
  workplaceType: '',
  dateRange: '',
};

const initialPagination: PaginationInfo = {
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 25,
  hasNextPage: false,
  hasPreviousPage: false,
};

const initialSorting: SortingInfo = {
  field: 'created_at',
  direction: 'desc',
};

export function useJobsList(): [JobsListState, JobsListActions] {
  const [jobs, setJobs] = useState<DatabaseJob[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [pagination, setPagination] =
    useState<PaginationInfo>(initialPagination);
  const [filters, setFilters] = useState<JobFilters>(initialFilters);
  const [sorting, setSorting] = useState<SortingInfo>(initialSorting);

  const buildQueryParams = useCallback(() => {
    const params = new URLSearchParams({
      page: pagination.currentPage.toString(),
      limit: pagination.itemsPerPage.toString(),
      sortField: sorting.field,
      sortDirection: sorting.direction,
    });

    // Add filters only if they have values
    for (const [key, value] of Object.entries(filters)) {
      if (value.trim()) {
        params.append(key, value);
      }
    }

    const _url = new URL('/api/jobs', window.location.origin);

    return params.toString();
  }, [pagination.currentPage, pagination.itemsPerPage, sorting, filters]);

  const loadJobs = useCallback(async () => {
    setLoading(true);
    setError('');

    try {
      const queryParams = buildQueryParams();
      const response = await fetch(`/api/jobs?${queryParams}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();

      setJobs(data.jobs || []);
      setPagination((prev) => ({
        ...prev,
        ...data.pagination,
      }));

      logger.info('Jobs loaded successfully', {
        count: data.jobs?.length || 0,
        totalItems: data.pagination?.totalItems || 0,
        currentPage: data.pagination?.currentPage || 1,
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load jobs';
      setError(errorMessage);
      logger.error('Failed to load jobs:', errorMessage);
      setJobs([]);
      setPagination(initialPagination);
    } finally {
      setLoading(false);
    }
  }, [buildQueryParams]);

  const setFilter = useCallback((key: keyof JobFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    // Reset to first page when filters change
    setPagination((prev) => ({ ...prev, currentPage: 1 }));
  }, []);

  const setSort = useCallback((field: string) => {
    setSorting((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'desc' ? 'asc' : 'desc',
    }));
  }, []);

  const setPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, currentPage: page }));
  }, []);

  const setItemsPerPage = useCallback((itemsPerPage: number) => {
    setPagination((prev) => ({
      ...prev,
      itemsPerPage,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  const refreshJobs = useCallback(async () => {
    await loadJobs();
  }, [loadJobs]);

  const clearError = useCallback(() => {
    setError('');
  }, []);

  // Load jobs when dependencies change
  useEffect(() => {
    loadJobs();
  }, [loadJobs]);

  const state: JobsListState = {
    jobs,
    loading,
    error,
    pagination,
    filters,
    sorting,
  };

  const actions: JobsListActions = {
    loadJobs,
    setFilter,
    setSort,
    setPage,
    setItemsPerPage,
    refreshJobs,
    clearError,
  };

  return [state, actions];
}
