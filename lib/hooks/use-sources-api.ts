import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import type { JobSource } from '@/components/sources/columns';

// Transform source data and add computed status
const transformApifySource = (source: JobSource): JobSource => ({
  ...source,
  status: (() => {
    if (source.stats.last_error) {
      return 'error';
    }
    if (source.stats.last_run_status === 'SUCCEEDED') {
      return 'active';
    }
    if (source.stats.last_run_status === 'FAILED') {
      return 'error';
    }
    return 'inactive';
  })(),
});

// Centralized error handling
const handleApiError = (error: unknown, defaultMessage: string): string => {
  return error instanceof Error ? error.message : defaultMessage;
};

// Generic toast pattern for async operations
const withLoadingToast = async <T>(
  operation: () => Promise<T>,
  loadingMessage: string,
  successMessage?: string
): Promise<T> => {
  const loadingToast = toast.loading(loadingMessage);

  try {
    const result = await operation();
    toast.dismiss(loadingToast);
    if (successMessage) {
      toast.success(successMessage);
    }
    return result;
  } catch (error) {
    toast.dismiss(loadingToast);
    throw error;
  }
};

export function useSourcesApi() {
  const [sources, setSources] = useState<JobSource[]>([]);
  const [loading, setLoading] = useState(true);

  // Centralized fetch logic
  const fetchSources = useCallback(async (): Promise<JobSource[]> => {
    const response = await fetch('/api/sources');
    const data = await response.json();

    if (!(data.success && data.sources)) {
      throw new Error(data.error || 'Failed to fetch sources');
    }

    return data.sources.map(transformApifySource);
  }, []);

  // Load sources initially
  useEffect(() => {
    const loadSources = async () => {
      try {
        const sourcesData = await fetchSources();
        setSources(sourcesData);
      } catch (error) {
        toast.error('Failed to load sources', {
          description: handleApiError(error, 'Unknown error occurred'),
        });
      } finally {
        setLoading(false);
      }
    };

    loadSources();
  }, [fetchSources]);

  // Refresh sources with toast notifications
  const refreshSources = useCallback(async (): Promise<void> => {
    await withLoadingToast(
      async () => {
        setLoading(true);
        try {
          const sourcesData = await fetchSources();
          setSources(sourcesData);
        } finally {
          setLoading(false);
        }
      },
      'Refreshing source statistics...',
      'Sources refreshed successfully!'
    );
  }, [fetchSources]);

  // Update single source status
  const updateSourceStatus = useCallback(
    (sourceId: string, updates: Partial<JobSource>) => {
      setSources((prev) =>
        prev.map((source) =>
          source.id === sourceId ? { ...source, ...updates } : source
        )
      );
    },
    []
  );

  return {
    sources,
    loading,
    setSources,
    refreshSources,
    updateSourceStatus,
    withLoadingToast,
    handleApiError,
  };
}
