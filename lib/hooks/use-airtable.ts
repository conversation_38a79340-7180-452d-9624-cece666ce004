'use client';

import { useCallback, useEffect, useState } from 'react';
import type { AirtableConfig, JobData, LoadingState } from '../types';
import { logger } from '../utils';

export function useAirtable() {
  const [loading, setLoading] = useState<LoadingState>('idle');
  const [error, setError] = useState<string>('');
  const [config, setConfig] = useState<AirtableConfig | null>(null);

  const loadConfig = useCallback(async () => {
    try {
      const response = await fetch('/api/airtable-config');
      const data = await response.json();
      setConfig(data);
    } catch (err) {
      logger.error('Failed to load Airtable config:', err);
      setError('Failed to load Airtable configuration');
    }
  }, []);

  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  const sendToAirtable = async (jobData: JobData) => {
    if (!config?.hasConfig) {
      setError('Airtable not configured. Please set environment variables.');
      return;
    }

    setLoading('loading');
    setError('');

    try {
      // Send job data directly to a new API endpoint that handles the Airtable sending
      const response = await fetch('/api/airtable-send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ jobData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `Failed to send data: ${response.status}`
        );
      }

      const result = await response.json();
      setLoading('success');
      return result;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to send data to Airtable';
      setError(errorMessage);
      setLoading('error');
      throw err;
    }
  };

  return {
    loading,
    error,
    config,
    sendToAirtable,
  };
}
