import {
  createBrowserClient,
  createServerClient as createSupabaseServerClient,
} from '@supabase/ssr';

// Client-side Supabase client (for use in Client Components)
export function createClient() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!(url && key)) {
    throw new Error('Missing Supabase environment variables');
  }

  return createBrowserClient(url, key);
}

// Server-side Supabase client (for use in Server Components, API routes)
export async function createServerClient() {
  const { cookies } = await import('next/headers');

  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!(url && key)) {
    throw new Error('Missing Supabase environment variables');
  }

  const cookieStore = await cookies();

  return createSupabaseServerClient(url, key, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet) {
        try {
          for (const { name, value, options } of cookiesToSet) {
            cookieStore.set(name, value, options);
          }
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  });
}

// Convenience export for the browser client
export const supabase = createClient();
