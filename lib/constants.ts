// Debug configuration
export const DEBUG_MODE = process.env.NODE_ENV === 'development';

// Consolidated AI configuration
export const AI_CONFIG = {
  MODEL: 'gpt-4o-mini',
  TEMPERATURE: 0.1,
  PRICING: {
    'gpt-4o-mini': { input: 0.15, output: 0.6 },
    'gpt-4o': { input: 2.5, output: 10.0 },
    heuristic: { input: 0, output: 0 }, // For non-AI monitoring methods
    error: { input: 0, output: 0 }, // For error cases
  },
} as const;

// Input limits
export const INPUT_LIMITS = {
  CONTENT_MAX_CHARS: 50_000,
  PROMPT_MAX_CHARS: 10_000,
} as const;

// Schema field character limits
export const SCHEMA_LIMITS = {
  benefits: 1500,
  application_requirements: 1000,
  skills: 500,
  qualifications: 800,
  education_requirements: 500,
  experience_requirements: 800,
  responsibilities: 1000,
} as const;

// Default values
export const DEFAULTS = {
  SOURCE_URL: 'https://job-site.com/posting',
  READING_WPM: 200, // words per minute for reading time estimation
} as const;

// Display configuration
export const DISPLAY_CONFIG = {
  CURRENCY: 'USD',
  COST_DECIMAL_PLACES: 6,
  TOKENS_PER_MILLION: 1_000_000,
} as const;

// Validate all required env vars are present
function validateEnvVars() {
  const required = [
    'QSTASH_TOKEN',
    'QSTASH_CURRENT_SIGNING_KEY',
    'QSTASH_NEXT_SIGNING_KEY',
    'SLACK_WEBHOOK_URL',
  ];

  const missing = required.filter((key) => !process.env[key]);
  if (missing.length > 0) {
    if (DEBUG_MODE) {
      return; // Don't throw in development
    }
    // Log warning instead of throwing error to prevent client-side crashes
    // Don't import logger here to avoid circular dependencies - use console in this critical case
    // biome-ignore lint/suspicious/noConsole: Critical environment validation requires console output
    console.warn(
      `⚠️ Missing environment variables: ${missing.join(
        ', '
      )}. Some features may not work properly.`
    );
    return false;
  }
  return true;
}

// Check if environment is properly configured
export const ENV_STATUS = {
  isFullyConfigured: validateEnvVars(),
  missingVars: (() => {
    const required = [
      'QSTASH_TOKEN',
      'QSTASH_CURRENT_SIGNING_KEY',
      'QSTASH_NEXT_SIGNING_KEY',
      'SLACK_WEBHOOK_URL',
    ];
    return required.filter((key) => !process.env[key]);
  })(),
};

// Upstash configuration with fallbacks for development
export const UPSTASH_CONFIG = {
  qstash: {
    token: process.env.QSTASH_TOKEN || '',
    currentSigningKey: process.env.QSTASH_CURRENT_SIGNING_KEY || '',
    nextSigningKey: process.env.QSTASH_NEXT_SIGNING_KEY || '',
  },
};

// Slack configuration with fallback
export const SLACK_CONFIG = {
  webhookUrl: process.env.SLACK_WEBHOOK_URL || '',
};

// Export validation function for manual use
export { validateEnvVars };
