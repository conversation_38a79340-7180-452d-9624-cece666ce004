// Job Board Configuration Constants
// Constants specific to job board posting and management

// Posting strategies for job boards
export const POSTING_STRATEGIES = [
  'newest_first',
  'best_match',
  'random',
] as const;

export type PostingStrategy = (typeof POSTING_STRATEGIES)[number];

export const POSTING_STRATEGY_DESCRIPTIONS: Record<PostingStrategy, string> = {
  newest_first: 'Post newest jobs first',
  best_match: 'Post jobs with highest relevance score first',
  random: 'Post jobs in random order',
} as const;

// Job source types for prioritization
export const JOB_SOURCES = [
  'wwr_rss',
  'jobdata_api',
  'manual_entry',
  'api_import',
  'csv_import',
] as const;

export type JobSource = (typeof JOB_SOURCES)[number];

export const JOB_SOURCE_DESCRIPTIONS: Record<JobSource, string> = {
  wwr_rss: 'We Work Remotely RSS Feed',
  jobdata_api: 'JobDataAPI.com Jobs',
  manual_entry: 'Manually entered jobs',
  api_import: 'Third-party API imports',
  csv_import: 'CSV file imports',
} as const;

// Common timezones for job boards
export const JOB_BOARD_TIMEZONES = [
  'UTC',
  'America/New_York', // EST/EDT
  'America/Chicago', // CST/CDT
  'America/Denver', // MST/MDT
  'America/Los_Angeles', // PST/PDT
  'America/Toronto', // EST/EDT (Canada)
  'Europe/London', // GMT/BST
  'Europe/Paris', // CET/CEST
  'Europe/Berlin', // CET/CEST
  'Europe/Madrid', // CET/CEST
  'Asia/Tokyo', // JST
  'Asia/Singapore', // SGT
  'Asia/Shanghai', // CST
  'Asia/Kolkata', // IST
  'Australia/Sydney', // AEST/AEDT
  'Australia/Melbourne', // AEST/AEDT
] as const;

export type JobBoardTimezone = (typeof JOB_BOARD_TIMEZONES)[number];

// Common posting times (24-hour format)
export const POSTING_TIMES = [
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
] as const;

export type PostingTime = (typeof POSTING_TIMES)[number];

// Recommended posting times for different regions
export const RECOMMENDED_POSTING_TIMES: Record<string, PostingTime[]> = {
  US_MORNING: ['09:00', '10:00', '11:00'],
  US_AFTERNOON: ['14:00', '15:00', '16:00'],
  EU_MORNING: ['08:00', '09:00', '10:00'],
  EU_AFTERNOON: ['13:00', '14:00', '15:00'],
  ASIA_MORNING: ['09:00', '10:00', '11:00'],
  GLOBAL_OPTIMAL: ['09:00', '15:00'], // Covers both US and EU business hours
} as const;

// Daily posting limits (common configurations)
export const DAILY_POSTING_LIMITS = {
  MINIMAL: 5,
  STANDARD: 10,
  ACTIVE: 15,
  AGGRESSIVE: 25,
  MAXIMUM: 50,
} as const;

// Reposting avoidance periods (in days)
export const REPOSTING_AVOIDANCE_DAYS = {
  SHORT: 7,
  STANDARD: 30,
  LONG: 45,
  EXTENDED: 60,
  PERMANENT: 365,
} as const;

// Job board status options
export const JOB_BOARD_STATUSES = [
  'active',
  'paused',
  'disabled',
  'testing',
] as const;

export type JobBoardStatus = (typeof JOB_BOARD_STATUSES)[number];

export const JOB_BOARD_STATUS_DESCRIPTIONS: Record<JobBoardStatus, string> = {
  active: 'Actively posting jobs',
  paused: 'Temporarily paused posting',
  disabled: 'Disabled - not posting',
  testing: 'Testing mode - limited posting',
} as const;

// Airtable table naming conventions
export const AIRTABLE_TABLE_NAMES = [
  'Jobs',
  'Job_Listings',
  'Opportunities',
  'Positions',
  'Openings',
] as const;

export type AirtableTableName = (typeof AIRTABLE_TABLE_NAMES)[number];

// Common job board categories/niches
export const JOB_BOARD_CATEGORIES = [
  'remote_general',
  'remote_tech',
  'remote_design',
  'remote_marketing',
  'remote_sales',
  'senior_engineering',
  'fullstack_development',
  'frontend_development',
  'backend_development',
  'data_science',
  'devops_sre',
  'product_management',
  'startup_jobs',
  'enterprise_jobs',
  'freelance_contract',
  'internships',
] as const;

export type JobBoardCategory = (typeof JOB_BOARD_CATEGORIES)[number];

export const JOB_BOARD_CATEGORY_DESCRIPTIONS: Record<JobBoardCategory, string> =
  {
    remote_general: 'General remote job opportunities',
    remote_tech: 'Remote technology positions',
    remote_design: 'Remote design and UX positions',
    remote_marketing: 'Remote marketing positions',
    remote_sales: 'Remote sales positions',
    senior_engineering: 'Senior+ engineering roles',
    fullstack_development: 'Full-stack development roles',
    frontend_development: 'Frontend development roles',
    backend_development: 'Backend development roles',
    data_science: 'Data science and analytics roles',
    devops_sre: 'DevOps and Site Reliability roles',
    product_management: 'Product management roles',
    startup_jobs: 'Startup and early-stage company jobs',
    enterprise_jobs: 'Enterprise and large company jobs',
    freelance_contract: 'Freelance and contract work',
    internships: 'Internship opportunities',
  } as const;

// Default job board configuration values
export const JOB_BOARD_DEFAULTS = {
  DAILY_LIMIT: DAILY_POSTING_LIMITS.STANDARD,
  POSTING_STRATEGY: 'newest_first' as PostingStrategy,
  AVOID_REPOSTING_DAYS: REPOSTING_AVOIDANCE_DAYS.STANDARD,
  TIMEZONE: 'UTC' as JobBoardTimezone,
  TABLE_NAME: 'Jobs' as AirtableTableName,
  STATUS: 'active' as JobBoardStatus,
} as const;
