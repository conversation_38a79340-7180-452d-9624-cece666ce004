import type { JobStatus } from './job-status';
import { createClient } from './supabase';
import type { JobData } from './types';
import { logger } from './utils';

// Initialize Supabase client
const supabase = createClient();

// Database job interface (includes the auto-generated fields)
export interface DatabaseJob extends JobData {
  id: string;
  created_at: string;
  updated_at: string;
  processing_status?: 'pending' | 'completed' | 'failed';

  // Monitoring fields
  monitor_status?: 'active' | 'closed' | 'filled' | 'unknown';
  monitor_attempts?: number;
  last_checked_at?: string;
  next_try_at?: string;
  raw_data_fetched_at?: string;
  deleted_at?: string;

  // Source tracking fields
  source_type?: string;
  source_name: string | null;
  source_id?: string;
  dedupe_key?: string;

  // Airtable integration fields
  airtable_synced_at?: string;
  airtable_id?: string;

  // Job boards integration fields
  // biome-ignore lint/suspicious/noExplicitAny: JSONB field can store any structure (array, object, etc.)
  posted_to_boards?: any; // JSONB field - could be array or other structure
  last_posted_at?: string;

  // Technical data fields
  ai_metadata?: Record<string, unknown>;
  raw_sourced_job_data?: Record<string, unknown>;

  // Database returns snake_case field names, so we include both for compatibility
  source_url?: string; // Database field name

  // ===== MISSING FIELDS FROM DATABASE =====
  // Tags and categorization
  // biome-ignore lint/suspicious/noExplicitAny: JSONB field can store any structure (array, object, etc.)
  tags?: any; // JSONB array field for categorization/tagging

  // Application methods
  apply_email?: string; // Email address for job applications

  // External identifiers
  external_id?: string; // External identifier from source systems

  // Processing timestamps
  processed_at?: string; // Timestamp when AI processing completed
}

// Storage metadata interface
export interface StorageMetadata {
  sourceUrl?: string;
  sourcedAt?: string;
  [key: string]: unknown;
}

// Mapping helper to transform JobData (camelCase) to DB record (snake_case)
function mapJobDataToDbRecord(jobData: JobData) {
  return {
    // Core job fields
    sourced_at: jobData.sourcedAt,
    source_url: jobData.sourceUrl,
    title: jobData.title,
    company: jobData.company,
    type: jobData.type,
    description: jobData.description,
    apply_url: jobData.apply_url,
    apply_method: jobData.apply_method,
    posted_date: jobData.posted_date,
    status: jobData.status,

    // Salary fields
    salary_min: jobData.salary_min,
    salary_max: jobData.salary_max,
    salary_currency: jobData.salary_currency,
    salary_unit: jobData.salary_unit,

    // Location fields
    workplace_type: jobData.workplace_type,
    remote_region: jobData.remote_region,
    timezone_requirements: jobData.timezone_requirements,
    workplace_city: jobData.workplace_city,
    workplace_country: jobData.workplace_country,

    // Additional details
    benefits: jobData.benefits,
    application_requirements: jobData.application_requirements,
    valid_through: jobData.valid_through,
    job_identifier: jobData.job_identifier,
    job_source_name: jobData.job_source_name,
    source_name: jobData.source_name,
    department: jobData.department,
    travel_required: jobData.travel_required,

    // Career fields
    career_level: jobData.career_level,
    visa_sponsorship: jobData.visa_sponsorship,
    languages: jobData.languages,
    skills: jobData.skills,
    qualifications: jobData.qualifications,
    education_requirements: jobData.education_requirements,
    experience_requirements: jobData.experience_requirements,
    responsibilities: jobData.responsibilities,

    // Classification
    featured: jobData.featured,
    industry: jobData.industry,
    occupational_category: jobData.occupational_category,
  } as Record<string, unknown>;
}

// Save processed job to Supabase
export async function saveProcessedJob(
  jobData: JobData,
  metadata: StorageMetadata,
  tags?: string[]
) {
  try {
    // Transform the job data for database insertion (snake_case)
    const jobRecord = {
      ...mapJobDataToDbRecord(jobData),
      // Store tags in JSONB field
      tags: tags || [],
      // Store processing metadata in ai_metadata JSONB field
      ai_metadata: {
        ...metadata,
        savedAt: new Date().toISOString(),
        source: {
          url: metadata.sourceUrl || 'unknown',
          sourcedAt: metadata.sourcedAt || new Date().toISOString(),
        },
        tagsCount: tags?.length || 0,
      },
    };

    const { data, error } = await supabase
      .from('jobs')
      .insert([jobRecord])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      id: data.id,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Get all saved jobs
export async function getAllSavedJobs(): Promise<DatabaseJob[]> {
  try {
    const { data, error } = await supabase
      .from('jobs')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    logger.error('Error fetching jobs:', error);
    return [];
  }
}

// Get job count
export async function getJobCount(): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    return count || 0;
  } catch (error) {
    logger.error('Error getting job count:', error);
    return 0;
  }
}

// Update job status
export async function updateJobStatus(
  jobId: string,
  newStatus: JobStatus,
  reason?: string
) {
  try {
    // First, get the current job to check previous status
    const { data: currentJob, error: fetchError } = await supabase
      .from('jobs')
      .select('status')
      .eq('id', jobId)
      .single();

    if (fetchError) {
      throw new Error(`Job with ID ${jobId} not found: ${fetchError.message}`);
    }

    const previousStatus = currentJob.status;

    // Update the job status
    const { data, error } = await supabase
      .from('jobs')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString(),
        ai_metadata: {
          lastStatusUpdate: {
            timestamp: new Date().toISOString(),
            reason: reason || 'Manual update',
            previousStatus,
          },
        },
      })
      .eq('id', jobId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      jobId: data.id,
      previousStatus,
      newStatus,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Get jobs by status
export async function getJobsByStatus(
  status: JobStatus
): Promise<DatabaseJob[]> {
  try {
    const { data, error } = await supabase
      .from('jobs')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    logger.error('Error fetching jobs by status:', error);
    return [];
  }
}

// Get jobs expiring soon
export async function getJobsExpiringInDays(
  days: number
): Promise<DatabaseJob[]> {
  try {
    const now = new Date();
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

    const { data, error } = await supabase
      .from('jobs')
      .select('*')
      .gte('valid_through', now.toISOString())
      .lte('valid_through', futureDate.toISOString())
      .order('valid_through', { ascending: true });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    logger.error('Error fetching expiring jobs:', error);
    return [];
  }
}

// Get jobs with statistics
export async function getJobStatistics() {
  try {
    const [total, draft, active, inactive, expired, filled, cancelled] =
      await Promise.all([
        getJobCount(),
        getJobsByStatus('draft').then((jobs) => jobs.length),
        getJobsByStatus('active').then((jobs) => jobs.length),
        getJobsByStatus('inactive').then((jobs) => jobs.length),
        getJobsByStatus('expired').then((jobs) => jobs.length),
        getJobsByStatus('filled').then((jobs) => jobs.length),
        getJobsByStatus('cancelled').then((jobs) => jobs.length),
      ]);

    return {
      total,
      draft,
      active,
      inactive,
      expired,
      filled,
      cancelled,
    };
  } catch (error) {
    logger.error('Error getting job statistics:', error);
    return {
      total: 0,
      draft: 0,
      active: 0,
      inactive: 0,
      expired: 0,
      filled: 0,
      cancelled: 0,
    };
  }
}

// Update existing job with AI processing results
export async function updateJobWithAIProcessing(
  jobId: string,
  extractedData: JobData,
  metadata: StorageMetadata
) {
  try {
    // First, check if the job exists
    logger.log('🔍 Checking if job exists:', jobId);
    const { data: existingJob, error: checkError } = await supabase
      .from('jobs')
      .select('id, title, processing_status')
      .eq('id', jobId)
      .single();

    if (checkError) {
      logger.error('❌ Job not found during check:', checkError);
      return {
        success: false,
        error: `Job with ID ${jobId} not found: ${checkError.message}`,
      };
    }

    logger.log('✅ Job found:', existingJob);

    // Only update AI-extracted fields, preserve original raw data fields
    const updateData = {
      ...mapJobDataToDbRecord(extractedData),
      processing_status: 'completed',
      ai_metadata: metadata,
      updated_at: new Date().toISOString(),
    };

    // Remove undefined values (nulls are allowed) to avoid Supabase errors for JSON columns
    const cleanUpdate = Object.fromEntries(
      Object.entries(updateData).filter(([, value]) => value !== undefined)
    );

    logger.log('🔍 Attempting to update job:', jobId);
    logger.log('📝 Update data:', JSON.stringify(cleanUpdate, null, 2));

    const { data, error } = await supabase
      .from('jobs')
      .update(cleanUpdate)
      .eq('id', jobId)
      .select()
      .single();

    if (error) {
      logger.error('❌ Supabase update error:', error);
      throw error;
    }

    logger.log('✅ Job updated successfully:', jobId);
    return {
      success: true,
      data,
    };
  } catch (error) {
    logger.error('💥 Update function error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
