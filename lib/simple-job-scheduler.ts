// Simplified Job Scheduler for MVP
// No complex scoring, no views, just the essentials

import {
  getActiveJobBoards,
  getJobBoardById,
  type JobBoardConfig,
  updateJobBoardStats,
} from './job-board-service';
import { getAirtablePat } from './secrets-manager';
import { createServerClient } from './supabase';
import type { JobData } from './types';
import { logger } from './utils';

interface PostingResult {
  jobId: string;
  boardId: string;
  success: boolean;
  error?: string;
}

interface SchedulerResult {
  boardId: string;
  boardName: string;
  jobsPosted: number;
  errors: string[];
}

/**
 * Check if job matches career level filter
 */
function matchesCareerLevel(job: JobData, board: JobBoardConfig): boolean {
  if (!(board.filters.careerLevels?.length && job.career_level)) {
    return true;
  }

  const hasMatchingLevel = job.career_level.some((level: string) =>
    board.filters.careerLevels?.includes(level)
  );

  if (!hasMatchingLevel) {
    return false;
  }

  return true;
}

/**
 * Check if job matches workplace type filter
 */
function matchesWorkplaceType(job: JobData, board: JobBoardConfig): boolean {
  return !(
    board.filters.workplaceTypes?.length &&
    job.workplace_type &&
    !board.filters.workplaceTypes.includes(job.workplace_type)
  );
}

/**
 * Check if job matches salary filters
 */
function matchesSalaryFilters(job: JobData, board: JobBoardConfig): boolean {
  // Salary minimum filter
  if (
    board.filters.salaryMin &&
    job.salary_min &&
    job.salary_min < board.filters.salaryMin
  ) {
    return false;
  }

  // Salary maximum filter
  if (
    board.filters.salaryMax &&
    job.salary_max &&
    job.salary_max > board.filters.salaryMax
  ) {
    return false;
  }

  return true;
}

/**
 * Check if job matches keyword filters
 */
function matchesKeywordFilters(job: JobData, board: JobBoardConfig): boolean {
  // Include keywords check
  if (board.filters.includeKeywords?.length) {
    const jobText = `${job.title} ${job.description} ${
      job.skills || ''
    }`.toLowerCase();

    const hasKeyword = board.filters.includeKeywords.some((keyword: string) =>
      jobText.includes(keyword.toLowerCase())
    );

    if (!hasKeyword) {
      return false;
    }
  }

  // Exclude keywords check
  if (board.filters.excludeKeywords?.length) {
    const jobText = `${job.title} ${job.description} ${
      job.skills || ''
    }`.toLowerCase();

    const hasExcluded = board.filters.excludeKeywords.some((keyword: string) =>
      jobText.includes(keyword.toLowerCase())
    );

    if (hasExcluded) {
      return false;
    }
  }

  return true;
}

/**
 * Check if job matches all board filters
 */
function matchesBoardFilters(job: JobData, board: JobBoardConfig): boolean {
  return (
    matchesCareerLevel(job, board) &&
    matchesWorkplaceType(job, board) &&
    matchesSalaryFilters(job, board) &&
    matchesKeywordFilters(job, board)
  );
}

/**
 * Get eligible jobs for a board using SQL-based filtering
 */
async function getEligibleJobsWithSQL(board: JobBoardConfig, limit = 50) {
  const supabase = await createServerClient();

  // Get active jobs from database
  const { data: jobs, error } = await supabase
    .from('jobs')
    .select('*')
    .eq('status', 'active')
    .limit(limit * 2); // Get more to account for filtering

  if (error) {
    throw new Error(`Database error: ${error.message}`);
  }

  if (!jobs) {
    return [];
  }

  // Apply board-specific filters
  const eligibleJobs = jobs.filter((job: JobData) =>
    matchesBoardFilters(job, board)
  );

  return eligibleJobs.slice(0, limit);
}

/**
 * Check daily limit - simple count query
 */
async function getTodaysPostCount(boardId: string): Promise<number> {
  const supabase = await createServerClient();
  const today = new Date().toISOString().split('T')[0];

  const { count } = await supabase
    .from('job_board_postings')
    .select('*', { count: 'exact', head: true })
    .eq('board_id', boardId)
    .eq('status', 'posted')
    .gte('posted_at', `${today}T00:00:00.000Z`)
    .lt('posted_at', `${today}T23:59:59.999Z`);

  return count || 0;
}

/**
 * Post job to Airtable
 */
async function postToAirtable(
  // biome-ignore lint/suspicious/noExplicitAny: Job data structure varies by source and needs dynamic handling
  job: any,
  board: JobBoardConfig
): Promise<string> {
  // Use secure PAT retrieval system
  const pat = await getAirtablePat(board.id);
  const baseId = board.airtable.baseId;
  const tableName = board.airtable.tableName;

  if (!(pat && baseId && tableName)) {
    throw new Error(`Airtable not configured for board ${board.id}`);
  }

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL || ''}/api/airtable-send`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jobData: job,
        airtableConfig: { pat, baseId, tableName },
      }),
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to post to Airtable');
  }

  const result = await response.json();
  return result.recordId || 'unknown';
}

/**
 * Record posting in database
 */
async function recordPosting(
  jobId: string,
  boardId: string,
  success: boolean,
  recordId?: string,
  error?: string
) {
  const supabase = await createServerClient();

  await supabase.from('job_board_postings').insert({
    job_id: jobId,
    board_id: boardId,
    status: success ? 'posted' : 'failed',
    airtable_record_id: recordId,
    error_message: error,
  });

  if (success) {
    // Update job's posted_to_boards array
    const { data: job } = await supabase
      .from('jobs')
      .select('posted_to_boards')
      .eq('id', jobId)
      .single();

    const boards = job?.posted_to_boards || [];
    if (!boards.includes(boardId)) {
      boards.push(boardId);
      await supabase
        .from('jobs')
        .update({
          posted_to_boards: boards,
          last_posted_at: new Date().toISOString(),
        })
        .eq('id', jobId);
    }

    // Update job board stats
    await updateJobBoardStats(boardId, 1);
  }
}

/**
 * Process one board - simplified
 */
async function processBoard(board: JobBoardConfig): Promise<PostingResult[]> {
  const results: PostingResult[] = [];

  // Check today's count
  const postedToday = await getTodaysPostCount(board.id);
  const remaining = board.posting.dailyLimit - postedToday;

  if (remaining <= 0) {
    logger.info(
      `Daily limit reached for ${board.id} (${postedToday}/${board.posting.dailyLimit})`
    );
    return results;
  }

  // Get eligible jobs
  const jobs = await getEligibleJobsWithSQL(board, remaining);
  const toPost = jobs.slice(0, remaining);

  logger.info(`Posting ${toPost.length} jobs to ${board.id}`);

  // Post jobs
  for (const job of toPost) {
    try {
      // biome-ignore lint/nursery/noAwaitInLoop: Sequential posting required to respect rate limits
      const recordId = await postToAirtable(job, board);
      await recordPosting(job.id, board.id, true, recordId);

      results.push({
        jobId: job.id,
        boardId: board.id,
        success: true,
      });

      logger.info(`✅ Posted ${job.title} to ${board.id}`);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      await recordPosting(job.id, board.id, false, undefined, errorMsg);

      results.push({
        jobId: job.id,
        boardId: board.id,
        success: false,
        error: errorMsg,
      });

      logger.error(`❌ Failed to post ${job.title} to ${board.id}:`, error);
    }
  }

  return results;
}

/**
 * Main scheduler - simplified
 */
export async function runSimpleJobScheduler(
  boardId?: string
): Promise<SchedulerResult[]> {
  const results: SchedulerResult[] = [];
  const _supabase = await createServerClient();

  try {
    // Get boards to process
    const boards = boardId
      ? [await getJobBoardById(boardId)].filter(Boolean)
      : await getActiveJobBoards();

    if (boards.length === 0) {
      logger.info('No active job boards to process');
      return results;
    }

    // Process boards in parallel
    const boardResults = await Promise.all(
      boards
        .filter((board): board is JobBoardConfig => board !== null)
        .map(async (board) => {
          const boardResult: SchedulerResult = {
            boardId: board.id,
            boardName: board.name,
            jobsPosted: 0,
            errors: [],
          };

          try {
            logger.info(`Processing board: ${board.name} (${board.id})`);

            // Get eligible jobs using new SQL approach
            const eligibleJobs = await getEligibleJobsWithSQL(
              board,
              board.posting.dailyLimit
            );

            logger.info(
              `Found ${eligibleJobs.length} eligible jobs for ${board.name}`
            );

            const postingResults = await processBoard(board);
            boardResult.jobsPosted = postingResults.filter(
              (r) => r.success
            ).length;
            boardResult.errors = postingResults
              .filter((r) => !r.success)
              .map((r) => r.error || 'Unknown error');

            logger.info(
              `📊 ${board.id}: ${boardResult.jobsPosted} posted, ${boardResult.errors.length} failed`
            );
          } catch (error) {
            logger.error(`💥 Error processing ${board.id}:`, error);
            boardResult.jobsPosted = 0;
            boardResult.errors = ['Error processing board'];
          }

          return boardResult;
        })
    );

    results.push(...boardResults);

    return results;
  } catch (error) {
    logger.error('💥 Error running scheduler:', error);
    return results;
  }
}
