import { openai } from '@ai-sdk/openai';
import { generateObject } from 'ai';
import { replaceTemplateVariables } from './api-utils';
import { AI_CONFIG } from './constants';
import { JobExtractionSchema } from './job-schema';
import type { JobStatus } from './job-status';
import { isJobExpired } from './job-status';
import { logger } from './logger';
import { JOB_EXTRACTION_PROMPT } from './prompts';
import { estimateReadingTime, generateMetadata } from './utils';

/**
 * Apply fallback logic for apply_url field
 */
function applyFallbackLogic(
  extractedJob: { apply_url?: string | null },
  sourceUrl: string
) {
  if (!extractedJob.apply_url || extractedJob.apply_url.trim() === '') {
    extractedJob.apply_url = sourceUrl;
  }
}

/**
 * Apply status logic based on job expiration
 */
function applyStatusLogic(extractedJob: {
  status?: JobStatus | null;
  valid_through?: string | null;
}) {
  // Set default status to active if not provided
  if (!extractedJob.status) {
    extractedJob.status = 'active';
  }

  // Auto-expire job if valid_through date has passed
  if (
    extractedJob.status === 'active' &&
    isJobExpired(extractedJob.valid_through || null)
  ) {
    extractedJob.status = 'expired';
  }
}

/**
 * Create metadata object for extraction result
 */
function createExtractionMetadata(
  startTime: number,
  usage: {
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
  },
  result: {
    response?: { id?: string; modelId?: string; timestamp?: Date };
    finishReason?: string;
  },
  post: { content?: string },
  finalPrompt: string
) {
  return generateMetadata(startTime, usage, {
    // Model details
    model: AI_CONFIG.MODEL,
    finishReason: result.finishReason || 'unknown',

    // Response metadata
    response: {
      id: result.response?.id || 'unknown',
      model: result.response?.modelId || AI_CONFIG.MODEL,
      timestamp: result.response?.timestamp || new Date(),
    },

    // Additional info
    contentLength: post.content?.length || 0,
    estimatedReadingTime: estimateReadingTime(post.content || ''),

    // Prompt info
    promptUsed: 'default',
    promptLength: finalPrompt.length,
  });
}

/**
 * Handle and process schema mismatch errors
 */
// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex error handling with multiple conditional checks is necessary for debugging
function handleSchemaError(error: Error) {
  if (!error.message.includes('response did not match schema')) {
    return;
  }

  // Try to extract debugging info from error cause
  if ('cause' in error && error.cause && typeof error.cause === 'object') {
    // Log the parsed value if available
    if ('value' in error.cause || 'parsedValue' in error.cause) {
      const _parsedValue =
        // biome-ignore lint/suspicious/noExplicitAny: Error cause properties can be any type from different error sources
        (error.cause as any).value || (error.cause as any).parsedValue;
    }

    if ('text' in error.cause) {
      // Try to parse the error response for debugging
      try {
        const _parsedResponse = JSON.parse(error.cause.text as string);
      } catch (_parseError) {
        // Silently ignore JSON parsing errors - error response parsing is for debugging only
      }
    }

    if ('issues' in error.cause) {
      // biome-ignore lint/suspicious/noExplicitAny: Zod error structure uses any for dynamic validation issues
      const cause = error.cause as { issues: any[] };

      // Log specific field validation errors
      if (Array.isArray(cause.issues)) {
        for (const [_index, _issue] of cause.issues.entries()) {
          // Process issue if needed
        }
      }
    }
  }
}

export async function extractJob(post: {
  sourcedAt: string;
  sourceUrl: string;
  content: string;
}) {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  // Log initial state for debugging
  logger.debug('Starting AI extraction', {
    contentLength: post.content.length,
    sourceUrl: post.sourceUrl,
    model: AI_CONFIG.MODEL,
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
    memoryStart: {
      heapUsed: `${startMemory.heapUsed / 1024 / 1024}MB`,
      heapTotal: `${startMemory.heapTotal / 1024 / 1024}MB`,
      external: `${startMemory.external / 1024 / 1024}MB`,
    },
  });

  // Use default prompt with placeholder replacement
  const finalPrompt = replaceTemplateVariables(JOB_EXTRACTION_PROMPT, {
    content: post.content,
  });

  logger.debug('Generated extraction prompt', {
    promptLength: finalPrompt.length,
    hasOpenAIKey: !!process.env.OPENAI_API_KEY,
    openaiKeyPrefix: process.env.OPENAI_API_KEY?.substring(0, 8) || 'undefined',
  });

  try {
    const result = await generateObject({
      model: openai(AI_CONFIG.MODEL),
      prompt: finalPrompt,
      schema: JobExtractionSchema,
    });

    const endMemory = process.memoryUsage();
    const duration = Date.now() - startTime;

    logger.debug('AI extraction successful', {
      duration: `${duration}ms`,
      inputTokens: result.usage?.inputTokens || 0,
      outputTokens: result.usage?.outputTokens || 0,
      totalTokens: result.usage?.totalTokens || 0,
      finishReason: result.finishReason,
      memoryUsed: `${
        (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024
      }MB`,
      memoryEnd: {
        heapUsed: `${endMemory.heapUsed / 1024 / 1024}MB`,
        heapTotal: `${endMemory.heapTotal / 1024 / 1024}MB`,
      },
    });

    // Apply fallback logic for apply_url and status logic
    const extractedJob = result.object as {
      apply_url?: string | null;
      status?: JobStatus | null;
      valid_through?: string | null;
    };
    applyFallbackLogic(extractedJob, post.sourceUrl);
    applyStatusLogic(extractedJob);

    // Use AI SDK v5 usage directly - no transformation needed
    const metadata = createExtractionMetadata(
      startTime,
      result.usage,
      result,
      post,
      finalPrompt
    );

    return {
      job: extractedJob,
      metadata,
      aiSdkResult: result, // Include the full AI SDK result for future extensibility
    };
  } catch (error) {
    const endMemory = process.memoryUsage();
    const duration = Date.now() - startTime;

    // Comprehensive error logging
    logger.critical('AI extraction failed', {
      error: error instanceof Error ? error.message : String(error),
      cause: error instanceof Error ? error.cause : undefined,
      stack:
        error instanceof Error
          ? error.stack?.split('\n').slice(0, 10)
          : undefined,
      duration: `${duration}ms`,
      inputSize: post.content.length,
      model: AI_CONFIG.MODEL,
      environment: process.env.NODE_ENV,
      nodeVersion: process.version,
      memoryUsed: `${
        (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024
      }MB`,
      memoryEnd: {
        heapUsed: `${endMemory.heapUsed / 1024 / 1024}MB`,
        heapTotal: `${endMemory.heapTotal / 1024 / 1024}MB`,
      },
      openaiKeyExists: !!process.env.OPENAI_API_KEY,
      promptLength: finalPrompt.length,
      sourceUrl: post.sourceUrl,
      // Include partial content for debugging (first 200 chars)
      contentPreview: post.content.substring(0, 200),
    });

    if (error instanceof Error) {
      handleSchemaError(error);
    }
    throw error;
  }
}
