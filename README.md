# Bordfeed - AI-Powered Job Board Automation Platform

A production-ready job board management and automation system built with Next.js 15 and OpenAI GPT-4o-mini. Features comprehensive job board configuration, intelligent filtering, automated scheduling, and multi-platform publishing with real-time monitoring.

## Key Features

🎯 **Multi-Board Job Automation** - Configure unlimited job boards with custom filtering, scheduling, and Airtable publishing  
🔍 **Smart Job Extraction** - AI-powered extraction of 35 structured fields from any job posting  
🎛️ **Comprehensive Filtering** - 68 currencies, 248 countries, 183 languages, 19 career levels, 13 remote regions  
📅 **Intelligent Scheduling** - Automated job posting with customizable daily limits and posting strategies  
🔐 **Encrypted PAT Storage** - AES-256-GCM encrypted Airtable Personal Access Tokens with secure management  
💾 **Professional Database** - PostgreSQL via Supabase with auto-save and real-time capabilities  
📝 **Optimized Prompts** - Production-tested extraction prompts for maximum accuracy  
📊 **Multi-Platform Publishing** - Direct export to multiple Airtable bases with schema validation  
⚡ **High Performance** - Next.js 15 with Turbopack, React 19, and optimized AI calls  
🌍 **Global Support** - Worldwide job market coverage with complete localization  
🏢 **Work Flexibility** - Remote, hybrid, on-site with geographic restrictions  
🎯 **Production Quality** - 100% Ultracite compliant with accessibility standards  
🔒 **Type Safety** - Full TypeScript with strict mode and Zod validation  
🎨 **Modern UI** - Complete shadcn/ui component library with accessible, customizable design system
🔄 **Multi-Source** - Apify-powered job sourcing with manual input, Workable XML, JobDataAPI, and WeWorkRemotely RSS  
🔁 **Database Deduplication** - Reliable duplicate detection using external_id uniqueness constraints  
📈 **Live Job Statistics** - Real-time counters and comprehensive monitoring dashboard  
🤖 **Job Status Monitoring** - Three-layer pipeline with HTTP checking, phrase matching, and AI classification

### Enhanced Data Flow

1. **Apify-Powered Data Ingestion** - Reliable job sourcing with built-in retry logic and monitoring
2. **Direct Database Storage** - Fast webhook processing with immediate database insertion and deduplication
3. **Scheduled AI Processing** - QStash-powered batch processing that updates pending jobs with extracted data
4. **Multi-Board Configuration** - Custom job boards with comprehensive filtering and scheduling
5. **Real-time Monitoring** - Live job status tracking with three-layer validation pipeline
6. **Secure Multi-Platform Publishing** - Encrypted credentials with multiple Airtable base support
7. **Automated Scheduling** - Smart job posting with daily limits and customizable strategies

## 🎨 Modern UI Design System

Built with **shadcn/ui** - a modern, accessible component library that provides:

### Component Library (18+ Components)
- **Core Inputs**: Button, Input, Textarea, Label, Select
- **Layout**: Card, Sidebar, Separator, Sheet, Tabs
- **Feedback**: Alert, Progress, Badge, Skeleton, Tooltip
- **Data Display**: Table, Chart (with Recharts integration)
- **Navigation**: Dropdown Menu, Dialog
- **Custom**: StatCard (built on shadcn/ui Card)

### Design Philosophy
- **"New York" Style** - Clean, modern aesthetic with subtle shadows and borders
- **Slate Color Palette** - Professional color scheme optimized for job board applications
- **Accessibility First** - Full ARIA compliance and keyboard navigation support
- **Customizable** - CSS variables for easy theming and brand adaptation
- **TypeScript Native** - Full type safety with variant-based component APIs

### Benefits Over Custom Components
- ✅ **Accessibility Built-in** - Screen reader support, keyboard navigation, focus management
- ✅ **Consistent Design** - Unified design language across all components  
- ✅ **Maintainable** - Community-driven updates and best practices
- ✅ **Performant** - Optimized bundle size with tree-shaking support
- ✅ **Future-Proof** - Active development and long-term support

## Tech Stack

- **Next.js** - React framework with App Router and Turbopack
- **React** - Latest React with concurrent features
- **TypeScript** - Strict type safety
- **Apify** - Reliable web scraping and data extraction and monitoring infrastructure
- **Supabase** - PostgreSQL database with real-time capabilities
- **OpenAI GPT-4o-mini** - AI-powered job data extraction
- **Vercel AI SDK** - Latest AI SDK with enhanced Zod v4 support
- **Zod** - Advanced schema validation and type inference
- **shadcn/ui** - Modern component library with accessible, customizable components
- **Tailwind CSS** - Modern utility-first styling foundation for shadcn/ui
- **Ultracite** - Comprehensive code quality and accessibility
- **Vercel** - Storing secrets, hosting and deployment
- **GitHub** - Version control and collaboration
- **pnpm** - Fast, disk space efficient package manager
- **Upstash** - QStash (queuing and job scheduling)
- **Airtable** - Primary job board publishing target
- **Slack** - For notifications

## Planned Integrations
- **Resend** - Email notifications (planned)
- **Outseta** - Auth, customer management, CRM, billing (planned)

## Quick Start

1. **Clone and install:**
   ```bash
   git clone <your-repo-url>
   cd bordfeed
   pnpm install
   ```

2. **Environment setup:**
   ```bash
   pnpm run env:pull # Pull all secrets from Vercel to .env.local
   ```

3. **Start development:**
   ```bash
   pnpm dev  # Starts on http://localhost:3000
   ```

Read more about environment variables in [docs/environment-variables.md](docs/environment-variables.md).

## 📡 API Endpoints

### Job Board Management
```
GET  /api/job-boards           # List all job board configurations
POST /api/job-boards           # Create new job board
PUT  /api/job-boards?id={id}   # Update job board configuration
DELETE /api/job-boards?id={id} # Delete job board
```

### Job Board Scheduling
```
GET  /api/job-scheduler        # Get scheduling status
POST /api/job-scheduler        # Trigger scheduled job posting
```

### Encrypted Airtable Credentials
```
GET  /api/airtable-secrets     # List encrypted PAT credentials
POST /api/airtable-secrets     # Store new encrypted PAT
PUT  /api/airtable-secrets     # Update encrypted PAT
DELETE /api/airtable-secrets   # Remove encrypted PAT
POST /api/airtable-test        # Test Airtable connection
```

### Core Extraction
```
POST /api/extract
```
Extract structured job data from raw text content.

**Request:**
```json
{
  "content": "Software Engineer position at TechCorp...",
  "sourceUrl": "https://company.com/jobs/123",
  "jobId": "uuid-optional-for-updates"
}
```

**Response:**
```json
{
  "job": {
    "title": "Software Engineer",
    "company": "TechCorp",
    "type": "Full-time",
    "description": "We are looking for...",
    "apply_url": "https://techcorp.com/apply/123",
    "apply_method": "link",
    "salary_min": 120000,
    "salary_max": 180000,
    "salary_currency": "USD",
    "salary_unit": "yearly",
    "workplace_type": "Remote",
    "remote_region": "Worldwide",
    "workplace_country": "United States",
    "department": "Engineering",
    "travel_required": false,
    "career_level": ["Senior"],
    "languages": ["en"],
    "skills": "JavaScript, React, Node.js, TypeScript",
    "status": "active",
    "visa_sponsorship": "not_specified",
    "posted_date": "2024-01-15T00:00:00Z",
    "valid_through": "2024-02-15T00:00:00Z"
  },
  "metadata": {
    "duration": 8210,
    "cost": { "total": 0.000972, "input": 0.000432, "output": 0.00054 },
    "usage": { "inputTokens": 2880, "outputTokens": 900, "totalTokens": 3780 },
    "model": "gpt-4o-mini",
    "saved": true,
    "jobId": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### AI Processing
```
POST /api/jobs/process-batch
```
QStash-scheduled AI processing endpoint that fetches pending jobs and updates them with extracted data.
Processes 10 jobs per batch every 5 minutes.

### Data Storage
```
POST /api/store-raw-jobs
```
Store raw job data for later processing **with database-level deduplication** using external_id uniqueness.
Returns a JSON `summary`:
```json
{ "successful": 3, "duplicated": 2, "failed": 0 }
```

### Data Sources Management
```
GET /api/sources                # Get all sources with statistics
POST /api/sources               # Create or update source configuration
GET /api/sources/{id}           # Get single source details
PATCH /api/sources/{id}         # Update source properties
POST /api/sources/{id}          # Log health check result
```
Manage job data sources with real-time statistics and health monitoring.

### Apify-Powered Data Ingestion
```
# Production-ready Apify integrations (✅ All completed)
POST /api/workable-webhook        # Workable Apify actor webhook
POST /api/workable-scheduler      # Trigger Workable actor run
POST /api/wwr-webhook            # WeWorkRemotely Apify actor webhook  
POST /api/wwr-scheduler          # Trigger WeWorkRemotely actor run
POST /api/jobdata-webhook        # JobDataAPI Apify actor webhook
POST /api/jobdata-scheduler      # Trigger JobDataAPI actor run
```
**All sources**: Production-ready with Apify actors, webhook integration, and automated scheduling

### Job Statistics
```
GET /api/job-stats
```
Retrieve high-level counts of saved jobs.

**Response:**
```json
{ "total": 123, "pending": 45, "completed": 78 }
```

### Airtable Integration
```
GET /api/airtable-config     # Check connection status
GET /api/airtable-schema     # Fetch table schema
POST /api/airtable-send      # Send job data to Airtable
```

## 🎯 Job Board Configuration

### Enhanced Filter System

**Comprehensive Filter Options:**
- **Job Types (9)** - Full-time, Part-time, Contract, Freelance, Internship, Temporary, Apprenticeship, Volunteer, Other
- **Workplace Types (3)** - On-site, Remote, Hybrid  
- **Remote Regions (13)** - Worldwide, Americas Only, Europe Only, Asia-Pacific Only, US Only, EU Only, UK/EU Only, US/Canada Only, LATAM Only, EMEA Only, APAC Only, English Speaking Only, Same Timezone Only
- **Career Levels (18)** - From Internship to C-Level and Founder
- **Currencies (68)** - All major fiat and cryptocurrencies (USD, EUR, BTC, ETH, etc.)
- **Countries (248)** - Complete worldwide coverage with smart search
- **Languages (183)** - ISO 639-1 language codes with automatic conversion
- **Keywords** - Include/exclude keyword filtering
- **Salary Ranges** - Minimum/maximum compensation filtering

### Posting Strategies
- **Newest First** - Prioritize recently posted jobs
- **Best Match** - AI-powered relevance scoring
- **Random** - Diverse job selection

### Automated Scheduling
- **Daily Limits** - Configurable posting frequency (1-50 jobs/day)
- **Smart Timing** - Optimal posting times based on board configuration
- **Duplicate Prevention** - Database-level deduplication using external_id uniqueness across all sources

## 🎯 Extracted Data Fields (35)

### Core Job Information
- **title** - Job title
- **company** - Company name
- **type** - Employment type (Full-time, Part-time, Contract, etc.)
- **description** - Full job description
- **status** - Job status (Active, Expired, Filled, etc.)

### Application Details
- **apply_url** - Application URL
- **apply_method** - How to apply (Link, Email, Phone, Form, Platform, Other)
- **application_requirements** - Specific application instructions

### Compensation
- **salary_min/max** - Salary range
- **salary_currency** - Currency code (USD, EUR, etc.)
- **salary_unit** - Time period (yearly, monthly, hourly, etc.)
- **benefits** - Employee benefits description

### Location & Work Arrangement
- **workplace_type** - On-site, Remote, Hybrid, Not specified
- **remote_region** - Geographic restrictions for remote work
- **timezone_requirements** - Timezone constraints
- **workplace_city/country** - Physical location
- **travel_required** - Business travel requirements

### Job Classification
- **department** - Team/department (Engineering, Marketing, etc.)
- **career_level** - Array of levels (Junior, Senior, Manager, etc.)
- **industry** - Industry classification
- **occupational_category** - Job category
- **visa_sponsorship** - Visa sponsorship availability

### Skills & Requirements
- **languages** - Array of required languages
- **skills** - Technical and soft skills
- **qualifications** - Required qualifications
- **education_requirements** - Educational background needed
- **experience_requirements** - Years of experience required
- **responsibilities** - Key job responsibilities

### Metadata
- **posted_date** - When job was posted
- **valid_through** - Application deadline
- **job_identifier** - External job ID
- **job_source_name** - Source platform name
- **featured** - Featured job flag
- **sourcedAt** - When extracted
- **sourceUrl** - Original job posting URL

## 🏗 Enhanced Project Structure

```
bordfeed/
├── app/
│   ├── api/
│   │   ├── extract/              # Core job extraction endpoint
│   │   ├── store-raw-jobs/       # Raw job data storage
│   │   ├── pipeline-ingest/      # Main data processing pipeline
│   │   ├── workable-webhook/     # Workable Apify actor webhook (✅ production)
│   │   ├── workable-scheduler/   # Workable actor scheduler (✅ production)
│   │   ├── wwr-webhook/          # WeWorkRemotely Apify webhook (✅ production)
│   │   ├── wwr-scheduler/        # WeWorkRemotely scheduler (✅ production)
│   │   ├── jobdata-webhook/      # JobDataAPI Apify webhook (✅ production)
│   │   ├── jobdata-scheduler/    # JobDataAPI scheduler (✅ production)
│   │   ├── dedup-test/           # Database deduplication testing endpoint
│   │   ├── dedup-stats/          # Database deduplication statistics API
│   │   ├── sources/              # Sources management API
│   │   ├── job-boards/           # Job board management API
│   │   ├── job-scheduler/        # Automated job scheduling
│   │   ├── airtable-secrets/     # Encrypted PAT management
│   │   ├── airtable-test/        # Airtable connection testing
│   │   ├── airtable-config/      # Airtable connection status
│   │   ├── airtable-schema/      # Airtable table schema
│   │   └── airtable-send/        # Send data to Airtable
│   ├── dashboard/
│   │   ├── sources/              # Database-driven Sources dashboard
│   │   ├── jobs/                 # Jobs management dashboard
│   │   ├── layout.tsx            # Dashboard layout with sidebar
│   │   └── page.tsx              # Main dashboard interface
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout with fonts
│   └── page.tsx                  # Application landing page
├── components/
│   ├── job-board.tsx             # Main tabbed interface
│   ├── tabs/
│   │   ├── extract-tab.tsx       # Job extraction interface
│   │   ├── airtable-tab.tsx      # Airtable integration
│   │   ├── database-tab.tsx      # Database management
│   │   └── monitor-tab.tsx       # Job monitoring
│   ├── sources/
│   │   ├── columns.tsx           # Sources table column definitions
│   │   ├── sources-stats.tsx     # Sources statistics cards
│   │   └── sources-table.tsx     # Database-driven sources table
│   └── ui/
│       ├── button.tsx            # shadcn/ui Button component
│       ├── input.tsx             # shadcn/ui Input component
│       ├── textarea.tsx          # shadcn/ui Textarea component
│       ├── card.tsx              # shadcn/ui Card component
│       ├── table.tsx             # shadcn/ui Table component
│       ├── progress.tsx          # shadcn/ui Progress component
│       ├── alert.tsx             # shadcn/ui Alert component
│       ├── dialog.tsx            # shadcn/ui Dialog component
│       ├── tabs.tsx              # shadcn/ui Tabs component
│       ├── sidebar.tsx           # shadcn/ui Sidebar component
│       ├── chart.tsx             # shadcn/ui Chart component
│       ├── stat-card.tsx         # Custom StatCard using shadcn/ui Card
│       └── [18+ more components] # Complete shadcn/ui component library
├── lib/
│   ├── hooks/
│   │   ├── use-job-extraction.ts    # Job extraction state management
│   │   └── use-airtable.ts          # Airtable operations
│   ├── services/
│   │   ├── deduplication-service.ts # Database-level deduplication utilities
│   │   ├── webhook-deduplication.ts # Webhook deduplication helpers
│   │   └── deduplication-debug.ts   # Deduplication testing and debugging
│   ├── extract-job.ts               # Core AI extraction logic
│   ├── job-schema.ts                # Zod schema definitions
│   ├── prompts.ts                   # Optimized AI prompts
│   ├── storage.ts                   # Supabase database operations
│   ├── supabase.ts                  # Supabase client configuration
│   ├── job-board-config.ts          # Job board configuration
│   ├── job-board-service.ts         # Job board operations
│   ├── job-posting-scheduler.ts     # Automated scheduling
│   ├── simple-job-scheduler.ts      # Scheduler implementation
│   ├── simple-queue.ts              # Queue management
│   ├── secrets-manager.ts           # Encrypted credentials
│   ├── constants.ts                 # Application constants
│   ├── types.ts                     # TypeScript type definitions
│   ├── utils.ts                     # shadcn/ui utils + re-exports for compatibility
│   ├── cost-calculator.ts           # AI cost calculation utilities
│   ├── logger.ts                    # Debug logging utilities
│   ├── metadata.ts                  # API metadata generation
│   ├── text-utils.ts                # Text processing utilities
│   ├── api-utils.ts                 # API helper functions
│   ├── validation.ts                # Request/response validation
│   └── [constant-files].ts          # Job types, countries, languages, etc.
└── [config files]                   # Next.js, TypeScript, Tailwind configs
```

## 💾 Enhanced Database Schema (Supabase)

### Core Tables

#### jobs
PostgreSQL table with comprehensive job data storage (35 fields + metadata)

#### job_board_configs  
Job board configuration with comprehensive filtering:
```sql
CREATE TABLE job_board_configs (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  daily_limit INTEGER DEFAULT 10,
  active BOOLEAN DEFAULT true,
  filters JSONB DEFAULT '{}',
  airtable_config JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### job_board_postings
Tracking posted jobs per board:
```sql
CREATE TABLE job_board_postings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  job_board_id TEXT REFERENCES job_board_configs(id),
  job_id UUID REFERENCES jobs(id),
  posted_at TIMESTAMP DEFAULT NOW(),
  airtable_record_id TEXT,
  status TEXT DEFAULT 'pending'
);
```

#### airtable_secrets
Encrypted Personal Access Token storage:
```sql
CREATE TABLE airtable_secrets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  encrypted_pat TEXT NOT NULL,
  base_id TEXT,
  table_name TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### job_monitor_logs
Job status monitoring with three-layer pipeline:
```sql
CREATE TABLE job_monitor_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  job_id UUID REFERENCES jobs(id),
  monitor_type TEXT NOT NULL,
  status TEXT NOT NULL,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### job_sources
Master configuration for data sources:
```sql
CREATE TABLE job_sources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  type TEXT NOT NULL,
  endpoint TEXT NOT NULL,
  enabled BOOLEAN DEFAULT true,
  description TEXT,
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### job_source_stats
Statistics tracking for each source:
```sql
CREATE TABLE job_source_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_id UUID REFERENCES job_sources(id),
  total_fetches INTEGER DEFAULT 0,
  successful_fetches INTEGER DEFAULT 0,
  total_jobs INTEGER DEFAULT 0,
  avg_response_time INTEGER DEFAULT 0,
  last_fetch_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### job_source_health_logs
Detailed activity logging for source operations:
```sql
CREATE TABLE job_source_health_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_id UUID REFERENCES job_sources(id),
  operation TEXT NOT NULL,
  status TEXT NOT NULL,
  response_time INTEGER,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔐 Security Features

### Encrypted Credential Storage
- **AES-256-GCM Encryption** - Military-grade encryption for Airtable PATs
- **Secure Key Management** - Environment-based encryption keys
- **Real-time Testing** - Live connection validation without credential exposure

### Data Protection
- **HTTPS-Only** - All communications encrypted in transit
- **Input Validation** - Comprehensive request/response validation with Zod
- **SQL Injection Prevention** - Parameterized queries and Supabase RLS
- **Rate Limiting** - API endpoint protection against abuse

### Access Control
- **Role-Based Access** - Supabase Row Level Security (RLS)
- **API Key Management** - Secure external service integration
- **Environment Isolation** - Separate development/production configurations

## 🚀 Performance & Costs

### AI Processing
- **Model**: OpenAI GPT-4o-mini
- **Response Time**: 8-12 seconds per extraction
- **Token Usage**: ~3,000-4,000 tokens per job
- **Cost**: ~$0.001 USD per extraction
- **Accuracy**: 95%+ on structured job postings

### Pricing
- **Input**: $0.15 per 1M tokens
- **Output**: $0.60 per 1M tokens
- **Monthly Est**: $10-30 for moderate usage
- **Database**: Free tier covers most use cases

### Optimization Features
- **Batch Processing** - Efficient multi-job operations
- **Intelligent Caching** - Reduced API calls through smart caching
- **Async Operations** - Non-blocking job processing
- **Progress Tracking** - Real-time processing feedback

## 🧪 Development & Testing

### Commands
```bash
pnpm dev          # Development server with Turbopack
pnpm build        # Production build
pnpm start        # Production server
pnpm lint         # Code quality check
pnpm format       # Code formatting
```

### Code Quality
- **100% Ultracite Compliance** - No linting errors
- **Type Safety** - Strict TypeScript, no `any` types
- **Accessibility** - Full ARIA compliance with shadcn/ui components
- **Performance** - Optimized patterns and best practices
- **DRY Code** - Modular utility architecture with focused single-responsibility modules
- **Modern UI** - shadcn/ui component library for consistent, accessible design

### Testing
- **Playwright** - End-to-end UI testing
- **API Testing** - Automated endpoint testing
- **Database Testing** - Supabase integration validation
- **Integration Testing** - Full workflow coverage
- **Security Testing** - Encryption and authentication validation

## 🕐 Automated Daily Schedule

Bordfeed operates on a fully automated schedule using QStash for reliable execution:

- **1:00 AM UTC** - JobDataAPI data ingestion (100 jobs, 7-day max age, remote only)
- **1:30 AM UTC** - WeWorkRemotely RSS feed ingestion (100 remote jobs via Cheerio Scraper)
- **2:00 AM UTC** - System monitoring and health checks  
- **3:00 AM UTC** - Workable global job feed (1000 jobs from last 24 hours)
- **Every 5 Minutes** - AI processing of pending jobs (10 jobs per batch)
- **6:00 AM, 2:00 PM, 10:00 PM UTC** - Job board posting (3x daily, 50 jobs each)

**Automation Benefits:**
- ✅ Unlimited schedules with QStash (vs 2 on Vercel Hobby)
- ✅ Per-second precision timing
- ✅ Built-in retries and error handling
- ✅ Request customization with body & headers
- ✅ Better monitoring and logging

## 🌍 Multi-Source Data Collection

### Apify-Powered Job Sourcing
Bordfeed uses **Apify Platform** for reliable, scalable job data ingestion with built-in retry logic, proxy rotation, and anti-bot protection.

### Current Data Sources

#### Manual Input (Primary)
- Direct paste of job postings
- Rich text processing
- Real-time extraction and preview

#### Workable XML Feed (via Apify)
- **✅ Production Ready** - Daily automated runs at 4am London time
- Global job feed with 1000+ fresh jobs daily
- Enterprise-grade scraping with webhook integration
- Cost: ~$0.02-0.03 per run (~$7-11/year)

#### WeWorkRemotely RSS (via Apify)
- **✅ Production Ready** - Fully migrated to Apify actors with 1:30 AM UTC daily scheduling
- Remote job specialization using custom actor for RSS parsing
- Enhanced reliability with webhook integration and error handling
- Cost-effective solution with predictable operational costs

#### JobDataAPI (via Apify)
- **✅ Production Ready** - Fully migrated to Apify actors with webhook integration
- Professional job data API with 1 AM UTC daily scheduling  
- Rate limiting (10 requests/hour free tier)
- Advanced filtering and search
- Structured data input

### Data Flow Architecture
```
Apify Actors → Webhooks → Database (pending) → QStash Scheduler → AI Processing (update) → Job Boards
```

**Benefits:**
- **Reliability**: Built-in retry logic and error handling
- **Scalability**: Handle thousands of sources without infrastructure load
- **Maintenance**: Zero scraping code maintenance
- **Cost-Effective**: Predictable costs vs server resource usage
- **Monitoring**: Real-time source health tracking

**Architecture Decision**: Moving all job sourcing to Apify allows Bordfeed to focus on its core differentiators - AI-powered job processing and multi-board automation - while leveraging Apify's expertise in reliable data extraction.

## 🔧 Environment Variables

```bash
# Required - OpenAI API
OPENAI_API_KEY=sk-your-openai-api-key

# Required - Supabase Database
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Required - Encryption for Airtable PATs
ENCRYPTION_KEY=your-32-character-encryption-key

# Required - Apify Platform
APIFY_TOKEN=your-apify-api-token

# Required - Apify Actor IDs for data sources
JOBDATAAPI_ACTOR_ID=your-jobdataapi-actor-id    # Custom JobDataAPI actor
WORKABLE_ACTOR_ID=your-workable-actor-id        # Custom Workable actor  
WWR_ACTOR_ID=your-wwr-actor-id                  # Custom WeWorkRemotely actor

# Optional - JobDataAPI (used by Apify actor)
JOBDATA_API_KEY=your-jobdata-api-key

# Optional - Debug Mode
NODE_ENV=development  # Enables detailed logging
```

## 📈 Deployment

### Vercel
1. Connect GitHub repository
2. Add environment variables in dashboard
3. Deploy automatically on push

### Changelog
For detailed version history, features, and changes, see **[CHANGELOG.md](CHANGELOG.md)**.