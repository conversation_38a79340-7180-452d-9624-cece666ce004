#!/usr/bin/env node

/**
 * Production Webhook Callback Testing Script
 *
 * Automated testing for webhook notification system after deployment
 * Uses real production endpoints and database connections
 */

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://bordfeed.com';
const _TEST_TIMEOUT = 30_000; // 30 seconds

/**
 * Test Results Tracker
 */
const results = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
};

function logTest(name, passed, details = '') {
  results.total++;
  if (passed) {
    results.passed++;
    if (details) {
      // Could log success details here if needed
    }
  } else {
    results.failed++;
    if (details) {
      // Could log failure details here if needed
    }
    results.errors.push({ test: name, details });
  }
}

/**
 * Phase 1: Pre-Production Health Checks
 */
async function testPhase1() {
  // Test 1.1: Webhook Callback Endpoint Health
  try {
    const response = await fetch(`${BASE_URL}/api/webhook-callbacks`, {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    });

    const data = await response.json();
    const hasCorrectFeatures =
      data.features?.includes('✅ Success callback handling') &&
      data.features?.includes('✅ QStash signature verification');

    logTest(
      'Webhook callback endpoint health',
      response.ok && hasCorrectFeatures,
      `Status: ${response.status}, Features: ${data.features?.length || 0}`
    );
  } catch (error) {
    logTest(
      'Webhook callback endpoint health',
      false,
      `Error: ${error.message}`
    );
  }

  // Test 1.2: Pipeline Ingest Endpoint Health
  try {
    const response = await fetch(`${BASE_URL}/api/pipeline-ingest`, {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    });

    const data = await response.json();
    const hasExpectedFeatures = data.features?.includes(
      '✅ QStash webhook verification'
    );

    logTest(
      'Pipeline ingest endpoint health',
      response.ok && hasExpectedFeatures,
      `Status: ${response.status}, Version: ${data.version || 'unknown'}`
    );
  } catch (error) {
    logTest(
      'Pipeline ingest endpoint health',
      false,
      `Error: ${error.message}`
    );
  }

  // Test 1.3: Environment Configuration Check
  try {
    const response = await fetch(`${BASE_URL}/api/health`, {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    });

    const data = await response.json();
    const hasQStashConfig = data.integrations?.qstash?.configured === true;
    const hasSlackConfig = data.integrations?.slack?.configured === true;

    logTest(
      'Environment configuration',
      hasQStashConfig && hasSlackConfig,
      `QStash: ${hasQStashConfig ? '✅' : '❌'}, Slack: ${
        hasSlackConfig ? '✅' : '❌'
      }`
    );
  } catch (error) {
    logTest('Environment configuration', false, `Error: ${error.message}`);
  }

  // Test 1.4: QStash Callback URL Reachability
  try {
    const response = await fetch(`${BASE_URL}/api/webhook-callbacks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Bordfeed-Test/1.0',
      },
      body: JSON.stringify({ test: 'reachability' }),
    });

    // Should get 401/403 (signature verification) not 404/500
    const isReachable = response.status === 401 || response.status === 403;

    logTest(
      'QStash callback URL reachability',
      isReachable,
      `Status: ${response.status} (expected 401/403 for signature verification)`
    );
  } catch (error) {
    logTest(
      'QStash callback URL reachability',
      false,
      `Error: ${error.message}`
    );
  }
}

/**
 * Phase 2: Database and Integration Tests
 */
async function testPhase2() {
  // Test 2.1: Recent Job Processing Check
  try {
    const response = await fetch(
      `${BASE_URL}/api/jobs?limit=5&sort=created_at&order=desc`,
      {
        method: 'GET',
        headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
      }
    );

    const data = await response.json();
    const hasRecentJobs = data.jobs?.length > 0;
    const recentJobWithSource = data.jobs?.some((job) =>
      ['jobdata_api', 'workable', 'WeWorkRemotely'].includes(job.source_name)
    );

    logTest(
      'Recent job processing check',
      hasRecentJobs,
      `Found ${data.jobs?.length || 0} recent jobs, Sources: ${
        recentJobWithSource ? '✅' : '❌'
      }`
    );
  } catch (error) {
    logTest('Recent job processing check', false, `Error: ${error.message}`);
  }

  // Test 2.2: Sources API Health
  try {
    const response = await fetch(`${BASE_URL}/api/sources`, {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    });

    const data = await response.json();
    const hasExpectedSources =
      data.sources?.some((s) => s.id === 'jobdata-api') &&
      data.sources?.some((s) => s.id === 'workable') &&
      data.sources?.some((s) => s.id === 'wwr-rss');

    logTest(
      'Sources API health',
      response.ok && hasExpectedSources,
      `Status: ${response.status}, Sources: ${data.sources?.length || 0}`
    );
  } catch (error) {
    logTest('Sources API health', false, `Error: ${error.message}`);
  }
}

/**
 * Phase 3: Webhook Handler Configuration Tests
 */
async function testPhase3() {
  const webhookEndpoints = [
    { name: 'JobDataAPI', path: '/api/jobdata-webhook' },
    { name: 'Workable', path: '/api/workable-webhook' },
    { name: 'WeWorkRemotely', path: '/api/wwr-webhook' },
  ];

  // Test all webhook endpoints in parallel
  const webhookTests = webhookEndpoints.map(async (webhook) => {
    try {
      const response = await fetch(`${BASE_URL}${webhook.path}`, {
        method: 'GET',
        headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
      });

      const data = await response.json();
      const hasCallbackFeatures =
        data.features?.some((f) => f.includes('callback')) ||
        data.bestPractices?.qstashIntegration?.includes('callback');

      logTest(
        `${webhook.name} webhook configuration`,
        response.ok && hasCallbackFeatures,
        `Status: ${response.status}, Callbacks: ${
          hasCallbackFeatures ? '✅' : '❌'
        }`
      );
    } catch (error) {
      logTest(
        `${webhook.name} webhook configuration`,
        false,
        `Error: ${error.message}`
      );
    }
  });

  // Wait for all webhook tests to complete
  await Promise.all(webhookTests);
}

/**
 * Phase 4: Mock Callback Test (Optional)
 */
async function testPhase4() {
  // Note: This would require valid QStash signature, so we'll just test endpoint format
  const mockCallbackData = {
    status: 200,
    method: 'POST',
    url: `${BASE_URL}/api/pipeline-ingest`,
    sourceMessageId: 'test_msg_123',
    retried: 0,
    createdAt: new Date().toISOString(),
    body: btoa(
      JSON.stringify({
        success: true,
        summary: {
          total: 1,
          successful: 1,
          failed: 0,
          duplicates: 0,
        },
      })
    ),
  };

  try {
    const response = await fetch(`${BASE_URL}/api/webhook-callbacks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Bordfeed-Test/1.0',
      },
      body: JSON.stringify(mockCallbackData),
    });

    // Expected to fail signature verification, but endpoint should be reachable
    const isProperlyProtected =
      response.status === 401 || response.status === 403;

    logTest(
      'Mock callback signature protection',
      isProperlyProtected,
      `Status: ${response.status} (proper signature verification)`
    );
  } catch (error) {
    logTest(
      'Mock callback signature protection',
      false,
      `Error: ${error.message}`
    );
  }
}

/**
 * Summary and Recommendations
 */
function printSummary() {
  if (results.failed > 0) {
    results.errors.forEach((_error, _index) => {
      // Could log individual errors here if needed
    });
  }

  if (results.passed === results.total) {
    // All tests passed - could log success message
  } else if (results.passed / results.total >= 0.8) {
    // Most tests passed - could log partial success
  } else {
    // Many tests failed - could log warning
  }
}

/**
 * Main Test Runner
 */
async function runAllTests() {
  const startTime = Date.now();

  try {
    await testPhase1();
    await testPhase2();
    await testPhase3();
    await testPhase4();

    const _duration = Date.now() - startTime;

    printSummary();

    // Exit with error code if tests failed
    process.exit(results.failed > 0 ? 1 : 0);
  } catch (_error) {
    process.exit(1);
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}

export { runAllTests };
