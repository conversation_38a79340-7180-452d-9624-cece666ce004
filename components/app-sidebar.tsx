'use client';

import {
  <PERSON><PERSON>,
  Code,
  FileText,
  Heart,
  Monitor,
  Radio,
  Search,
  Settings,
  Target,
} from 'lucide-react';
import type * as React from 'react';
import { NavDevelopment } from '@/components/nav-development';
import { NavMain } from '@/components/nav-main';
import { NavProjects } from '@/components/nav-projects';
import { NavUser } from '@/components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';

// Bordfeed navigation data
const data = {
  user: {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatar.png',
  },
  navMain: [
    {
      title: 'Sources',
      url: '/dashboard/sources',
      icon: Radio,
      isActive: true,
      items: [],
    },
    {
      title: 'Jobs',
      url: '/dashboard/jobs',
      icon: FileText,
      items: [],
    },
  ],
  management: [
    {
      name: 'Health',
      url: '/dashboard/health',
      icon: Heart,
    },
    {
      name: 'Monitor',
      url: '/dashboard/monitor',
      icon: Monitor,
    },

    {
      name: 'Job Boards',
      url: '/dashboard/boards',
      icon: Target,
    },
  ],
  development: [
    {
      name: 'Airtable',
      url: '/dashboard/dev',
      icon: Code,
    },
    {
      name: 'AI Extract',
      url: '/dashboard/dev/extract',
      icon: Search,
    },
    {
      name: 'Settings',
      url: '/dashboard/dev/settings',
      icon: Settings,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              size="lg"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Bot className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">Bordfeed</span>
                <span className="truncate text-xs">
                  AI-Powered Job Board Automation
                </span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.management} />
        <NavDevelopment items={data.development} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
