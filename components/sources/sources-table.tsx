'use client';

import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Settings } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { DataTableBase } from '@/components/ui/data-table-base';
import { useDataTable } from '@/lib/hooks/use-data-table';
import { useSourcesApi } from '@/lib/hooks/use-sources-api';
import { getTestUrl } from '@/lib/sources-config';
import { createSourcesColumns, type JobSource } from './columns';

// Helper function to log health data to database
const logHealthData = async (
  sourceId: string,
  // biome-ignore lint/suspicious/noExplicitAny: Health data structure varies by source type and test scenario
  healthData: any
): Promise<void> => {
  try {
    await fetch(`/api/sources/${sourceId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(healthData),
    });
  } catch {
    // Silently ignore logging errors - health logging is not critical
  }
};

// Helper function to handle connection test error response
const handleTestErrorResponse = async (response: Response): Promise<string> => {
  let errorMessage = `Source returned status ${response.status}`;

  try {
    const errorData = await response.json();
    if (response.status === 429 && errorData.details) {
      errorMessage = errorData.details;
    } else if (response.status === 429) {
      errorMessage = 'Rate limit exceeded. Free tier allows 10 requests/hour.';
    } else if (errorData.error) {
      errorMessage = errorData.error;
    }
  } catch {
    // Use default message if JSON parsing fails
  }

  return errorMessage;
};

export function SourcesTable() {
  // Use the DRY API hook
  const {
    sources,
    loading,
    refreshSources,
    updateSourceStatus,
    withLoadingToast,
  } = useSourcesApi();

  // Use the DRY data table hook
  const dataTableState = useDataTable({
    defaultSorting: [{ id: 'name', desc: false }],
    defaultColumnVisibility: {
      // Show most important columns by default
      name: true,
      type: true,
      status: true,
      success_rate: true,
      total_jobs: true,
      // Hide detailed fields by default (can be toggled via View button)
      endpoint: false,
      rate_limit: false,
      data_quality: false,
      avg_processing_time: false,
    },
  });

  // Helper function to execute connection test
  const executeConnectionTest = useCallback(
    async (sourceId: string, testUrl: string) => {
      const startTime = Date.now();
      const response = await fetch(testUrl);
      const responseTime = Date.now() - startTime;
      const status: JobSource['status'] = response.ok ? 'active' : 'error';

      // Log health check to database
      await logHealthData(sourceId, {
        status: response.ok ? 'success' : 'error',
        response_time_ms: responseTime,
        jobs_returned: 0, // Just a test connection
        error_message: response.ok ? undefined : `HTTP ${response.status}`,
        metadata: {
          test_url: testUrl,
          method: 'GET',
        },
      });

      return { response, status };
    },
    []
  );

  const handleTestConnection = useCallback(
    async (sourceId: string): Promise<void> => {
      const source = sources.find((s) => s.id === sourceId);
      if (!source) {
        return;
      }

      const loadingToast = toast.loading(
        `Testing connection to ${source.name}...`
      );

      updateSourceStatus(sourceId, { status: 'inactive' });

      try {
        const testUrl = getTestUrl(sourceId);

        if (!testUrl) {
          toast.dismiss(loadingToast);
          toast.info(`No test available for ${source.name}`);
          updateSourceStatus(sourceId, { status: 'active' });
          return;
        }

        const { response, status } = await executeConnectionTest(
          sourceId,
          testUrl
        );

        updateSourceStatus(sourceId, { status });

        toast.dismiss(loadingToast);

        if (response.ok) {
          toast.success('Connection test successful!', {
            description: `${source.name} is responding correctly`,
          });
        } else {
          const errorMessage = await handleTestErrorResponse(response);
          toast.error('Connection test failed', {
            description: errorMessage,
          });
        }
      } catch (error) {
        updateSourceStatus(sourceId, { status: 'error' });

        // Log error to database
        await logHealthData(sourceId, {
          status: 'error' as const,
          error_message:
            error instanceof Error ? error.message : 'Unknown error',
          metadata: {
            source_id: sourceId,
            error_type: 'connection_test',
          },
        });

        toast.dismiss(loadingToast);
        toast.error('Connection test failed', {
          description: `${source.name}: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
        });
      }
    },
    [sources, executeConnectionTest, updateSourceStatus]
  );

  const sourcesColumns = useMemo(
    () => createSourcesColumns(handleTestConnection),
    [handleTestConnection]
  );

  const table = useReactTable({
    data: sources,
    columns: sourcesColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    ...dataTableState.getTableProps(),
  });

  const testSingleSource = useCallback(
    async (source: JobSource): Promise<JobSource> => {
      try {
        const testUrl = getTestUrl(source.id);
        if (!testUrl) {
          return { ...source, status: 'active' };
        }

        const response = await fetch(testUrl);
        const status: JobSource['status'] = response.ok ? 'active' : 'error';
        return { ...source, status };
      } catch {
        return { ...source, status: 'error' };
      }
    },
    []
  );

  const handleTestAllSources = useCallback(async (): Promise<void> => {
    await withLoadingToast(
      async () => {
        const testPromises = sources.map(testSingleSource);
        const updatedSources = await Promise.all(testPromises);

        // Count results
        const activeCount = updatedSources.filter(
          (s) => s.enabled && s.status === 'active'
        ).length;
        const errorCount = updatedSources.filter(
          (s) => s.enabled && s.status === 'error'
        ).length;

        if (errorCount === 0) {
          toast.success('All sources tested successfully!', {
            description: `${activeCount} enabled sources are working correctly`,
          });
        } else {
          toast.warning('Some sources failed testing', {
            description: `${activeCount} working, ${errorCount} failed`,
          });
        }
      },
      `Testing ${sources.filter((s) => s.enabled).length} enabled sources...`,
      'Sources tested successfully!'
    );
  }, [sources, testSingleSource, withLoadingToast]);

  return (
    <DataTableBase
      customActions={
        <Button onClick={handleTestAllSources} size="sm">
          <Settings className="mr-2 h-4 w-4" />
          Test All
        </Button>
      }
      filterColumn="name"
      filterPlaceholder="Filter sources..."
      loading={loading}
      noDataMessage="No sources found."
      onRefresh={refreshSources}
      refreshLabel="Refresh All"
      showSelection={false}
      table={table}
    />
  );
}
