'use client';

import {
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from '@tanstack/react-table';
import { Plus, RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DataTablePagination } from '../jobs/data-table-pagination';
import { DataTableViewOptions } from '../jobs/data-table-view-options';
import { createBoardsColumns, type JobBoard } from './columns';
import { CreateBoardDialog } from './create-board-dialog';
import { EditBoardDialog } from './edit-board-dialog';
import { PatManagementDialog } from './pat-management-dialog';

export function BoardsTable() {
  const [boards, setBoards] = useState<JobBoard[]>([]);
  const [loading, setLoading] = useState(true);
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  // Modal states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showPatDialog, setShowPatDialog] = useState(false);
  const [selectedBoard, setSelectedBoard] = useState<JobBoard | null>(null);

  // PAT management state
  const [patManagement, setPatManagement] = useState<{
    availablePats: Record<string, string[]>;
    storageMethod: string;
  } | null>(null);

  // Load boards from API
  const loadBoards = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/job-boards');
      if (!response.ok) {
        throw new Error(`Failed to load job boards: ${response.status}`);
      }

      const data = await response.json();
      const boardsData = Array.isArray(data) ? data : data.boards || [];

      // Transform boards and add PAT status
      // biome-ignore lint/suspicious/noExplicitAny: API response structure varies and needs dynamic property access
      const transformedBoards: JobBoard[] = boardsData.map((board: any) => ({
        ...board,
        patStatus: (patManagement?.availablePats[board.id]
          ? 'configured'
          : 'missing') as 'configured' | 'missing',
      }));

      setBoards(transformedBoards);
    } catch (error) {
      toast.error('Failed to load job boards', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
      setBoards([]);
    } finally {
      setLoading(false);
    }
  }, [patManagement]);

  // Load PAT management info
  const loadPatManagement = useCallback(async () => {
    try {
      const response = await fetch('/api/airtable-secrets');
      if (response.ok) {
        const data = await response.json();
        setPatManagement({
          availablePats: data.availablePats || {},
          storageMethod: data.storageMethod || 'env_vars',
        });
      }
    } catch (_error) {
      // Silently ignore PAT management errors - not critical for board functionality
    }
  }, []);

  // biome-ignore lint/suspicious/noExplicitAny: Job board configuration can have dynamic structure from form data
  const createJobBoard = async (config: any) => {
    try {
      const response = await fetch('/api/job-boards', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`Failed to create job board: ${response.status}`);
      }

      await loadBoards();
      setShowCreateDialog(false);
      toast.success('Job board created successfully');
    } catch (error) {
      toast.error('Failed to create job board', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Update job board
  const updateJobBoard = useCallback(
    async (updatedBoard: JobBoard) => {
      try {
        const response = await fetch('/api/job-boards', {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedBoard),
        });

        if (!response.ok) {
          throw new Error(`Failed to update job board: ${response.status}`);
        }

        await loadBoards();
        toast.success('Job board updated successfully');
      } catch (error) {
        toast.error('Failed to update job board', {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
    [loadBoards]
  );

  // Delete job board
  const deleteJobBoard = useCallback(
    async (id: string) => {
      if (
        !confirm(
          'Are you sure you want to delete this job board? This action cannot be undone.'
        )
      ) {
        return;
      }

      try {
        const response = await fetch(`/api/job-boards?id=${id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete job board: ${response.status}`);
        }

        await loadBoards();
        toast.success('Job board deleted successfully');
      } catch (error) {
        toast.error('Failed to delete job board', {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
    [loadBoards]
  );

  // Toggle job board enabled status
  const toggleEnabled = useCallback(
    async (board: JobBoard) => {
      const updatedBoard = { ...board, enabled: !board.enabled };
      await updateJobBoard(updatedBoard);
    },
    [updateJobBoard]
  );

  // Store PAT for board
  const storePat = async (boardId: string, pat: string) => {
    try {
      const response = await fetch('/api/airtable-secrets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ boardId, pat }),
      });

      if (!response.ok) {
        throw new Error(`Failed to store PAT: ${response.status}`);
      }

      await loadPatManagement();
      await loadBoards();
      setShowPatDialog(false);
      setSelectedBoard(null);
      toast.success('PAT stored successfully');
    } catch (error) {
      toast.error('Failed to store PAT', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test connection
  const testConnection = useCallback(async (board: JobBoard) => {
    if (board.patStatus !== 'configured') {
      toast.error('Cannot test connection', {
        description: 'PAT not configured for this board',
      });
      return;
    }

    const loadingToast = toast.loading(
      `Testing connection to ${board.name}...`
    );

    try {
      const response = await fetch('/api/airtable-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          baseId: board.airtable.baseId,
          tableName: board.airtable.tableName,
          boardId: board.id,
        }),
      });

      toast.dismiss(loadingToast);

      if (response.ok) {
        const data = await response.json();

        // Enhanced success message with record count and performance info
        const recordCount = data.records?.totalCount || 'unknown';
        const responseTime = data.performance?.responseTime || 'unknown';
        const tableName = data.config?.tableName || board.airtable.tableName;

        toast.success('Connection test successful!', {
          description: `Connected to ${tableName} table, ${recordCount} records available (${responseTime})`,
          duration: 5000, // Show longer to give time to read the details
        });
      } else {
        const errorData = await response.json();
        toast.error('Connection test failed', {
          description: errorData.error || `Status ${response.status}`,
          duration: 6000, // Show error longer
        });
      }
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error('Connection test failed', {
        description: error instanceof Error ? error.message : 'Unknown error',
        duration: 6000,
      });
    }
  }, []);

  // Action handlers
  const handleEdit = useCallback((board: JobBoard) => {
    setSelectedBoard(board);
    setShowEditDialog(true);
  }, []);

  const handleManagePat = useCallback((board: JobBoard) => {
    setSelectedBoard(board);
    setShowPatDialog(true);
  }, []);

  const boardsColumns = useMemo(
    () =>
      createBoardsColumns(
        handleEdit,
        toggleEnabled,
        deleteJobBoard,
        handleManagePat,
        testConnection
      ),
    [deleteJobBoard, handleEdit, handleManagePat, testConnection, toggleEnabled]
  );

  const table = useReactTable({
    data: boards,
    columns: boardsColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  useEffect(() => {
    loadPatManagement();
  }, [loadPatManagement]);

  useEffect(() => {
    loadBoards();
  }, [loadBoards]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="h-8 w-48 animate-pulse rounded bg-muted" />
          <div className="h-10 w-32 animate-pulse rounded bg-muted" />
        </div>
        <div className="rounded-md border">
          <div className="h-[400px] w-full animate-pulse bg-muted" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with search and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            className="max-w-sm"
            onChange={(event) =>
              table.getColumn('name')?.setFilterValue(event.target.value)
            }
            placeholder="Filter boards..."
            value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
          />
          <Button onClick={loadBoards} size="sm" variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <DataTableViewOptions table={table} />
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Job Board
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  data-state={row.getIsSelected() && 'selected'}
                  key={row.id}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell className="min-w-0" key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  className="h-24 text-center"
                  colSpan={boardsColumns.length}
                >
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <div className="text-2xl">🎯</div>
                    <div className="font-medium text-lg text-muted-foreground">
                      No job boards configured
                    </div>
                    <div className="text-muted-foreground text-sm">
                      Create your first job board to start automatically posting
                      jobs
                    </div>
                    <Button
                      className="mt-2"
                      onClick={() => setShowCreateDialog(true)}
                    >
                      Create Your First Job Board
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />

      {/* Modal Dialogs */}
      <CreateBoardDialog
        onClose={() => setShowCreateDialog(false)}
        onSubmit={createJobBoard}
        open={showCreateDialog}
      />

      {selectedBoard && (
        <>
          <EditBoardDialog
            board={selectedBoard}
            onClose={() => {
              setShowEditDialog(false);
              setSelectedBoard(null);
            }}
            onSubmit={updateJobBoard}
            open={showEditDialog}
          />

          <PatManagementDialog
            board={selectedBoard}
            onClose={() => {
              setShowPatDialog(false);
              setSelectedBoard(null);
            }}
            onSubmit={(pat) => storePat(selectedBoard.id, pat)}
            open={showPatDialog}
          />
        </>
      )}
    </div>
  );
}
