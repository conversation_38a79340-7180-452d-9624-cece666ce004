'use client';

import type { ColumnDef } from '@tanstack/react-table';
import {
  ArrowUpDown,
  Key,
  Pause,
  Play,
  Settings,
  TestTube,
  Trash2,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
// Using built-in date formatting instead of date-fns
import type { JobBoardConfig } from '@/lib/job-board-service';

export type JobBoard = JobBoardConfig & {
  patStatus: 'configured' | 'missing';
};

export function createBoardsColumns(
  onEdit: (board: JobBoard) => void,
  onToggle: (board: JobBoard) => void,
  onDelete: (id: string) => void,
  onManagePat: (board: JobBoard) => void,
  onTestConnection: (board: JobBoard) => void
): ColumnDef<JobBoard>[] {
  return [
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            className="h-auto p-0 font-semibold"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            variant="ghost"
          >
            Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const board = row.original;
        return (
          <div className="space-y-1">
            <div className="font-medium">{board.name}</div>
            {board.description && (
              <div className="text-muted-foreground text-sm">
                {board.description}
              </div>
            )}
            <div className="font-mono text-muted-foreground text-xs">
              #{board.id}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'enabled',
      header: 'Status',
      cell: ({ row }) => {
        const enabled = row.getValue('enabled') as boolean;
        return (
          <Badge variant={enabled ? 'default' : 'secondary'}>
            {enabled ? 'Active' : 'Paused'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'posting',
      header: 'Posting Config',
      cell: ({ row }) => {
        const board = row.original;
        return (
          <div className="space-y-1">
            <div className="text-sm">
              <span className="font-medium">{board.posting.dailyLimit}</span>{' '}
              jobs/day
            </div>
            <div className="text-muted-foreground text-xs capitalize">
              {board.posting.strategy.replace('_', ' ')}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'airtable',
      header: 'Airtable',
      cell: ({ row }) => {
        const board = row.original;
        return (
          <div className="space-y-1">
            <div className="font-medium text-sm">
              {board.airtable.tableName}
            </div>
            <div className="font-mono text-muted-foreground text-xs">
              {board.airtable.baseId}
            </div>
            <Badge
              className="text-xs"
              variant={
                board.patStatus === 'configured' ? 'default' : 'destructive'
              }
            >
              {board.patStatus === 'configured' ? 'Connected' : 'No PAT'}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'filters',
      header: 'Filters',
      cell: ({ row }) => {
        const filters = row.original.filters;
        const filterCount = Object.keys(filters).filter((key) => {
          const value = filters[key as keyof typeof filters];
          return Array.isArray(value) ? value.length > 0 : !!value;
        }).length;

        if (filterCount === 0) {
          return <Badge variant="outline">No filters</Badge>;
        }

        return (
          <div className="space-y-1">
            <Badge variant="secondary">{filterCount} filters</Badge>
            {filters.types && filters.types.length > 0 && (
              <div className="text-muted-foreground text-xs">
                Types: {filters.types.slice(0, 2).join(', ')}
                {filters.types.length > 2 && ` +${filters.types.length - 2}`}
              </div>
            )}
            {filters.includeKeywords && filters.includeKeywords.length > 0 && (
              <div className="text-muted-foreground text-xs">
                Keywords: {filters.includeKeywords.slice(0, 2).join(', ')}
                {filters.includeKeywords.length > 2 &&
                  ` +${filters.includeKeywords.length - 2}`}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'totalPosted',
      header: ({ column }) => {
        return (
          <Button
            className="h-auto p-0 font-semibold"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            variant="ghost"
          >
            Posted
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const totalPosted = row.getValue('totalPosted') as number;
        const lastPosted = row.original.lastPostedAt;

        return (
          <div className="space-y-1">
            <div className="font-medium text-sm">{totalPosted || 0}</div>
            <div className="text-muted-foreground text-xs">
              {lastPosted ? new Date(lastPosted).toLocaleDateString() : 'Never'}
            </div>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const board = row.original;

        return (
          <div className="flex items-center space-x-1">
            <Button
              className="h-8 px-2"
              onClick={() => onToggle(board)}
              size="sm"
              variant="outline"
            >
              {board.enabled ? (
                <>
                  <Pause className="mr-1 h-3 w-3" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="mr-1 h-3 w-3" />
                  Start
                </>
              )}
            </Button>

            <Button
              className="h-8 px-2"
              onClick={() => onManagePat(board)}
              size="sm"
              variant="outline"
            >
              <Key className="mr-1 h-3 w-3" />
              {board.patStatus === 'configured' ? 'Update' : 'Connect'}
            </Button>

            <Button
              className="h-8 px-2"
              disabled={board.patStatus !== 'configured'}
              onClick={() => onTestConnection(board)}
              size="sm"
              variant="outline"
            >
              <TestTube className="mr-1 h-3 w-3" />
              Test
            </Button>

            <Button
              className="h-8 px-2"
              onClick={() => onEdit(board)}
              size="sm"
              variant="outline"
            >
              <Settings className="mr-1 h-3 w-3" />
              Edit
            </Button>

            <Button
              className="h-8 px-2 text-destructive hover:bg-destructive hover:text-destructive-foreground"
              onClick={() => onDelete(board.id)}
              size="sm"
              variant="outline"
            >
              <Trash2 className="mr-1 h-3 w-3" />
              Delete
            </Button>
          </div>
        );
      },
    },
  ];
}
