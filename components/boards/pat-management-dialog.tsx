'use client';

import { <PERSON>, <PERSON>Off, Key } from 'lucide-react';
import { useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { JobBoard } from './columns';

interface PatManagementDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (pat: string) => Promise<void>;
  board: JobBoard;
}

export function PatManagementDialog({
  open,
  onClose,
  onSubmit,
  board,
}: PatManagementDialogProps) {
  const [pat, setPat] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPat, setShowPat] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!pat.trim()) {
      setError('PAT is required');
      return;
    }

    if (!pat.startsWith('pat') || pat.length < 20) {
      setError(
        'Invalid PAT format. PATs should start with "pat" and be at least 20 characters'
      );
      return;
    }

    setLoading(true);
    try {
      await onSubmit(pat);
      setPat('');
    } catch (submitError) {
      setError(
        submitError instanceof Error ? submitError.message : 'Unknown error'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog onOpenChange={onClose} open={open}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            {board.patStatus === 'configured' ? 'Update' : 'Configure'} PAT
          </DialogTitle>
          <DialogDescription>
            {board.patStatus === 'configured'
              ? `Update the Personal Access Token for "${board.name}"`
              : `Configure Personal Access Token for "${board.name}"`}
          </DialogDescription>
        </DialogHeader>

        <Alert>
          <AlertDescription className="text-sm">
            You'll need to create a Personal Access Token (PAT) in your Airtable
            account. Go to{' '}
            <strong>
              Account Settings → Developer Hub → Personal access tokens
            </strong>
            and create a token with read/write access to your base.
          </AlertDescription>
        </Alert>

        <form className="space-y-4" onSubmit={handleSubmit}>
          {error && (
            <div className="rounded-md bg-destructive/15 px-3 py-2 text-destructive text-sm">
              {error}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="pat">Personal Access Token *</Label>
            <div className="relative">
              <Input
                className="pr-10"
                id="pat"
                onChange={(e) => setPat(e.target.value)}
                placeholder="pat..."
                required
                type={showPat ? 'text' : 'password'}
                value={pat}
              />
              <Button
                className="absolute top-1 right-1 h-7 w-7 px-0"
                onClick={() => setShowPat(!showPat)}
                size="sm"
                type="button"
                variant="ghost"
              >
                {showPat ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            <p className="text-muted-foreground text-xs">
              Your PAT will be encrypted and stored securely
            </p>
          </div>

          <div className="rounded-md bg-muted p-3">
            <div className="font-medium text-sm">Board Details:</div>
            <div className="mt-1 space-y-1 text-muted-foreground text-xs">
              <div>Base ID: {board.airtable.baseId}</div>
              <div>Table: {board.airtable.tableName}</div>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={onClose} type="button" variant="outline">
              Cancel
            </Button>
            <Button disabled={loading} type="submit">
              {(() => {
                if (loading) {
                  return 'Storing...';
                }
                if (board.patStatus === 'configured') {
                  return 'Update PAT';
                }
                return 'Store PAT';
              })()}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
