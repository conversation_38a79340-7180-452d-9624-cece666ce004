'use client';

import { Bar<PERSON>hart3, CheckCircle, Target, Zap } from 'lucide-react';
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

type BasicBoard = {
  enabled?: boolean;
  posting?: { dailyLimit?: number };
};

interface BoardStats {
  totalBoards: number;
  activeBoards: number;
  dailyCapacity: number;
  totalPosted: number;
}

export function BoardsStats() {
  const [stats, setStats] = React.useState<BoardStats>({
    totalBoards: 0,
    activeBoards: 0,
    dailyCapacity: 0,
    totalPosted: 0,
  });
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const loadStats = async () => {
      try {
        const response = await fetch('/api/job-boards');
        const data = await response.json();

        if (data.success && data.boards) {
          const boards = data.boards;
          const newStats: BoardStats = {
            totalBoards: boards.length,
            activeBoards: boards.filter((b: BasicBoard) => b.enabled).length,
            dailyCapacity: boards.reduce(
              (sum: number, b: BasicBoard) =>
                sum + (b.posting?.dailyLimit || 0),
              0
            ),
            totalPosted: boards.reduce(
              // biome-ignore lint/suspicious/noExplicitAny: Board data structure is flexible
              (sum: number, b: any) => sum + (b.totalPosted || 0),
              0
            ),
          };
          setStats(newStats);
        }
      } catch (_error) {
        // Silently ignore errors - stats are not critical
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, []);

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {['total', 'enabled', 'posted-today', 'success-rate'].map(
          (statType) => (
            <Card key={statType}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="font-medium text-sm">
                  <div className="h-4 w-20 animate-pulse rounded bg-muted" />
                </CardTitle>
                <div className="h-4 w-4 animate-pulse rounded bg-muted" />
              </CardHeader>
              <CardContent>
                <div className="mb-1 h-8 w-16 animate-pulse rounded bg-muted" />
                <div className="h-3 w-24 animate-pulse rounded bg-muted" />
              </CardContent>
            </Card>
          )
        )}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Boards</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">{stats.totalBoards}</div>
          <p className="text-muted-foreground text-xs">Configured job boards</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Active Boards</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl text-green-600">
            {stats.activeBoards}
          </div>
          <p className="text-muted-foreground text-xs">Currently enabled</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Daily Capacity</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl text-purple-600">
            {stats.dailyCapacity}
          </div>
          <p className="text-muted-foreground text-xs">Jobs per day limit</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Posted</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl text-orange-600">
            {stats.totalPosted}
          </div>
          <p className="text-muted-foreground text-xs">All-time job posts</p>
        </CardContent>
      </Card>
    </div>
  );
}
