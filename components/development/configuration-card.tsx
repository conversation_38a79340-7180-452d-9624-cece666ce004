'use client';

import { Settings } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface ConfigurationCardProps {
  config: {
    baseId?: string | null;
    tableName?: string | null;
    hasPat?: boolean;
    hasConfig?: boolean;
  } | null;
}

export function ConfigurationCard({ config }: ConfigurationCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Settings className="h-5 w-5" />
          Configuration Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        {config ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium text-xs">Base ID:</span>
              <code className="rounded bg-muted px-2 py-1 text-xs">
                {config.baseId || 'Not configured'}
              </code>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium text-xs">Table:</span>
              <code className="rounded bg-muted px-2 py-1 text-xs">
                {config.tableName || 'Not configured'}
              </code>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium text-xs">PAT:</span>
              <Badge variant={config.hasPat ? 'default' : 'destructive'}>
                {config.hasPat ? '✅ Configured' : '❌ Not configured'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium text-xs">Status:</span>
              <Badge variant={config.hasConfig ? 'default' : 'destructive'}>
                {config.hasConfig ? '✅ Ready' : '❌ Incomplete'}
              </Badge>
            </div>
          </div>
        ) : (
          <div className="text-muted-foreground">Loading configuration...</div>
        )}
      </CardContent>
    </Card>
  );
}
