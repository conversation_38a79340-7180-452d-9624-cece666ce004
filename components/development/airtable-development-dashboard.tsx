'use client';

import { useCallback, useEffect, useState } from 'react';
import { useAirtable } from '../../lib/hooks/use-airtable';
import { ConfigurationCard } from './configuration-card';
import { ConfigurationHelpCard } from './configuration-help-card';
import { DebugCard } from './debug-card';
import { FieldMappingCard } from './field-mapping-card';
import { SchemaCard } from './schema-card';
import { SendDataCard } from './send-data-card';

// Airtable types for component state
interface AirtableFieldChoice {
  id: string;
  name: string;
  color?: string;
}

interface AirtableField {
  id: string;
  name: string;
  type: string;
  options?: {
    choices?: AirtableFieldChoice[];
    [key: string]: unknown;
  };
}

export interface AirtableSchemaData {
  success: boolean;
  tableName: string;
  tableId: string;
  fields: AirtableField[];
  allTables: string[];
}

export interface AirtableSendResult {
  error?: string;
  records?: Array<{ id: string; [key: string]: unknown }>;
  [key: string]: unknown;
}

export function AirtableDevelopmentDashboard() {
  const [schemaData, setSchemaData] = useState<AirtableSchemaData | null>(null);
  const { config } = useAirtable();

  const loadSchema = useCallback(async () => {
    try {
      const response = await fetch('/api/airtable-schema');
      const data = await response.json();
      setSchemaData(data);
    } catch (_err) {
      // Silently ignore errors - schema loading is not critical
    }
  }, []);

  useEffect(() => {
    if (config?.hasConfig) {
      loadSchema();
    }
  }, [config, loadSchema]);

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <ConfigurationCard config={config} />
      {schemaData && <SchemaCard schemaData={schemaData} />}
      <SendDataCard className="md:col-span-2" config={config} />
      <FieldMappingCard className="md:col-span-2" schemaData={schemaData} />
      <DebugCard />
      <ConfigurationHelpCard />
    </div>
  );
}
