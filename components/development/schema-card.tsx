'use client';

import { Code, Database } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';
import type { AirtableSchemaData } from './airtable-development-dashboard';

interface SchemaCardProps {
  schemaData: AirtableSchemaData;
}

export function SchemaCard({ schemaData }: SchemaCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Database className="h-5 w-5" />
          Table Schema
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <div className="flex justify-between">
            <span className="font-medium text-xs">Table:</span>
            <code className="text-xs">{schemaData.tableName}</code>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-xs">Fields:</span>
            <span className="text-xs">
              {schemaData.fields?.length || 0} total
            </span>
          </div>
        </div>

        <Collapsible>
          <CollapsibleTrigger asChild>
            <Button className="w-full justify-between" variant="outline">
              Field Names ({schemaData.fields?.length || 0})
              <Code className="h-4 w-4" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <Card className="mt-4">
              <CardContent className="max-h-60 overflow-y-auto p-4">
                <div className="space-y-2">
                  {schemaData.fields?.map((field) => (
                    <div className="text-xs" key={field.id}>
                      <div className="flex items-center justify-between">
                        <code className="font-mono">{field.name}</code>
                        <Badge variant="outline">{field.type}</Badge>
                      </div>
                      {(field.type === 'singleSelect' ||
                        field.type === 'multipleSelects') &&
                        field.options?.choices && (
                          <div className="ml-4 text-muted-foreground text-xs">
                            Options:{' '}
                            {field.options.choices
                              .slice(0, 3)
                              .map((choice) => choice.name)
                              .join(', ')}
                            {field.options.choices.length > 3 &&
                              ` +${field.options.choices.length - 3} more`}
                          </div>
                        )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
