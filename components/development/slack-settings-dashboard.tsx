'use client';

import {
  Activity,
  AlertCircle,
  Bell,
  CheckCircle,
  MessageSquare,
  Settings,
  Zap,
} from 'lucide-react';
import { useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export function SlackSettingsDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const [lastTestResult, setLastTestResult] = useState<{
    success: boolean;
    message: string;
    timestamp: string;
  } | null>(null);

  const sendTestNotification = async (
    type: 'critical' | 'alert' | 'notify' | 'pipeline'
  ) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-slack', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type }),
      });

      const result = await response.json();
      setLastTestResult({
        success: response.ok,
        message: result.message || result.error || 'Unknown response',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      setLastTestResult({
        success: false,
        message: `Network error: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testAllNotifications = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-slack', {
        method: 'POST',
      });

      const result = await response.json();
      setLastTestResult({
        success: response.ok,
        message: result.message || result.error || 'Unknown response',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      setLastTestResult({
        success: false,
        message: `Network error: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Slack Configuration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Slack Configuration
          </CardTitle>
          <CardDescription>
            Current Slack webhook configuration and connection status
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium text-sm">Webhook URL</span>
            <Badge className="text-green-600" variant="outline">
              <CheckCircle className="mr-1 h-3 w-3" />
              Configured
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="font-medium text-sm">Environment</span>
            <Badge variant="secondary">
              {process.env.NODE_ENV || 'development'}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="font-medium text-sm">Debug Mode</span>
            <Badge
              variant={
                process.env.NODE_ENV === 'development' ? 'default' : 'outline'
              }
            >
              {process.env.NODE_ENV === 'development' ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Test Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Test Notifications
          </CardTitle>
          <CardDescription>
            Send test notifications to verify Slack integration is working
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Individual Test Buttons */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Button
              className="flex items-center gap-2"
              disabled={isLoading}
              onClick={() => sendTestNotification('critical')}
              variant="destructive"
            >
              <AlertCircle className="h-4 w-4" />
              Test Critical Alert
            </Button>

            <Button
              className="flex items-center gap-2 border-orange-200 text-orange-600 hover:bg-orange-50"
              disabled={isLoading}
              onClick={() => sendTestNotification('alert')}
              variant="outline"
            >
              <AlertCircle className="h-4 w-4" />
              Test Warning Alert
            </Button>

            <Button
              className="flex items-center gap-2"
              disabled={isLoading}
              onClick={() => sendTestNotification('notify')}
              variant="secondary"
            >
              <MessageSquare className="h-4 w-4" />
              Test Info Notification
            </Button>

            <Button
              className="flex items-center gap-2 border-blue-200 text-blue-600 hover:bg-blue-50"
              disabled={isLoading}
              onClick={() => sendTestNotification('pipeline')}
              variant="outline"
            >
              <Activity className="h-4 w-4" />
              Test Pipeline Steps
            </Button>
          </div>

          <Separator />

          {/* Test All Button */}
          <div className="flex flex-col space-y-3">
            <Button
              className="flex items-center gap-2"
              disabled={isLoading}
              onClick={testAllNotifications}
              size="lg"
            >
              <Zap className="h-4 w-4" />
              {isLoading ? 'Sending Tests...' : 'Send All Test Notifications'}
            </Button>

            <p className="text-center text-muted-foreground text-xs">
              This will send critical, alert, info, and pipeline test
              notifications
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {lastTestResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Last Test Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant={lastTestResult.success ? 'default' : 'destructive'}>
              <div className="flex items-center gap-2">
                {lastTestResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  <div className="space-y-1">
                    <div className="font-medium">
                      {lastTestResult.success ? 'Success!' : 'Error'}
                    </div>
                    <div>{lastTestResult.message}</div>
                    <div className="text-xs opacity-70">
                      {new Date(lastTestResult.timestamp).toLocaleString()}
                    </div>
                  </div>
                </AlertDescription>
              </div>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Pipeline Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Pipeline Notification Types
          </CardTitle>
          <CardDescription>
            Available pipeline step notifications for job processing monitoring
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                step: 'SOURCED',
                emoji: '📥',
                description: 'Jobs scraped from Apify actors',
              },
              {
                step: 'DEDUPED',
                emoji: '🔍',
                description: 'Duplicate jobs filtered out',
              },
              {
                step: 'QUEUED',
                emoji: '📤',
                description: 'Jobs queued to processing pipeline',
              },
              {
                step: 'PROCESSED',
                emoji: '🧠',
                description: 'AI extraction completed',
              },
              {
                step: 'STORED',
                emoji: '💾',
                description: 'Jobs saved to database',
              },
              {
                step: 'MONITORED',
                emoji: '👁️',
                description: 'Job monitoring executed',
              },
              {
                step: 'SYNCED',
                emoji: '🔄',
                description: 'Airtable sync completed',
              },
            ].map((item) => (
              <div className="space-y-2 rounded-lg border p-3" key={item.step}>
                <div className="flex items-center gap-2">
                  <span className="text-lg">{item.emoji}</span>
                  <Badge className="text-xs" variant="outline">
                    {item.step}
                  </Badge>
                </div>
                <p className="text-muted-foreground text-xs">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Integration Examples</CardTitle>
          <CardDescription>
            How to use these notifications in your job processing pipeline
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium text-sm">
              Critical Alerts (Production Issues)
            </h4>
            <pre className="rounded bg-muted p-2 text-xs">
              {`logger.critical("Database connection failed", {
  error: error.message,
  service: "pipeline-ingest"
});`}
            </pre>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm">Pipeline Step Tracking</h4>
            <pre className="rounded bg-muted p-2 text-xs">
              {`logger.pipeline({
  step: "PROCESSED",
  jobCount: 25,
  success: true,
  stats: { succeeded: 23, failed: 2 }
});`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
