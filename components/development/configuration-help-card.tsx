'use client';

import { HelpCircle } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';

export function ConfigurationHelpCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <HelpCircle className="h-5 w-5" />
          Configuration Help
        </CardTitle>
        <CardDescription className="text-xs">
          To enable Airtable integration, you need to configure these
          environment variables:
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <ol className="list-inside list-decimal space-y-2 text-xs">
          <li>
            Create a Personal Access Token at{' '}
            <a
              className="text-primary underline underline-offset-4"
              href="https://airtable.com/developers/web/api/personal-access-tokens"
              rel="noopener noreferrer"
              target="_blank"
            >
              Airtable PAT page
            </a>
          </li>
          <li>Get your Base ID from your Airtable base URL</li>
          <li>Set the table name you want to use</li>
          <li>Add these to your .env.local file</li>
        </ol>
        <Card>
          <CardContent className="p-4">
            <pre className="font-mono text-xs">
              {`AIRTABLE_PAT=pat1234...
AIRTABLE_BASE_ID=appABC123...
AIRTABLE_TABLE_NAME=Jobs`}
            </pre>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  );
}
