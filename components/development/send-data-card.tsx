'use client';

import { Send } from 'lucide-react';
import { useState } from 'react';
import { useAirtable } from '../../lib/hooks/use-airtable';
import type { JobData } from '../../lib/types';
import { cn } from '../../lib/utils';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Textarea } from '../ui/textarea';
import type { AirtableSendResult } from './airtable-development-dashboard';

interface SendDataCardProps {
  config: {
    baseId?: string | null;
    tableName?: string | null;
    hasPat?: boolean;
    hasConfig?: boolean;
  } | null;
  className?: string;
}

function createSampleJobData() {
  return {
    title: 'Senior Software Engineer',
    company: 'TechCorp Inc.',
    workplace_type: 'Remote',
    type: 'Full-time',
    salary_min: 120_000,
    salary_max: 150_000,
    salary_currency: 'USD',
    salary_unit: 'year',
    description: 'We are looking for a Senior Software Engineer...',
    apply_url: 'https://techcorp.com/careers/senior-engineer',
    skills: 'React, TypeScript, Node.js, PostgreSQL',
    department: 'Engineering',
    travel_required: false,
  };
}

function SendResults({
  sendResult,
  error,
}: {
  sendResult: AirtableSendResult | null;
  error: string | null;
}) {
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (sendResult) {
    return (
      <Alert variant={sendResult.error ? 'destructive' : 'default'}>
        <AlertDescription>
          {sendResult.error ? (
            sendResult.error
          ) : (
            <div>
              <div className="font-medium">
                ✅ Successfully sent to Airtable!
              </div>
              <div className="text-xs">
                Record ID: {sendResult.records?.[0]?.id}
              </div>
            </div>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return null;
}

export function SendDataCard({ config, className }: SendDataCardProps) {
  const [jobDataInput, setJobDataInput] = useState('');
  const [sendResult, setSendResult] = useState<AirtableSendResult | null>(null);
  const { loading, error, sendToAirtable } = useAirtable();

  const handleSendData = async () => {
    if (!jobDataInput.trim()) {
      setSendResult({ error: 'Please enter job data' });
      return;
    }

    try {
      const jobData = JSON.parse(jobDataInput) as JobData;
      const result = await sendToAirtable(jobData);
      setSendResult(result);
    } catch (_err) {
      setSendResult({ error: 'Invalid JSON format' });
    }
  };

  const loadSampleData = () => {
    const sampleJobData = createSampleJobData();
    setJobDataInput(JSON.stringify(sampleJobData, null, 2));
  };

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Send className="h-5 w-5" />
          Send Job Data
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="font-medium text-xs" htmlFor="job-data-input">
              Job Data (JSON)
            </label>
            <Button onClick={loadSampleData} size="sm" variant="outline">
              Load Sample
            </Button>
          </div>
          <Textarea
            className="min-h-[300px] font-mono text-xs"
            id="job-data-input"
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
              setJobDataInput(e.target.value)
            }
            placeholder={`Paste extracted job data here, e.g.:
{
  "title": "Software Engineer",
  "company": "TechCorp",
  "salary_min": 80000,
  "salary_max": 120000,
  "workplace_type": "remote",
  "skills": "React, TypeScript",
  ...
}`}
            value={jobDataInput}
          />
        </div>

        <Button
          className="w-full"
          disabled={
            !(jobDataInput.trim() && config?.hasConfig) || loading === 'loading'
          }
          onClick={handleSendData}
        >
          {loading === 'loading' ? 'Sending...' : 'Send to Airtable'}
        </Button>

        <SendResults error={error} sendResult={sendResult} />
      </CardContent>
    </Card>
  );
}
