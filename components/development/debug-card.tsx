'use client';

import { Bug, Code } from 'lucide-react';
import { useState } from 'react';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';

interface DebugResult {
  success: boolean;
  error?: string;
  suggestion?: string;
  availableBases?: Array<{ id: string; name: string }>;
  availableTables?: string[];
  baseName?: string;
  tableName?: string;
  fieldsCount?: number;
  sampleFields?: string[];
  config?: object;
}

export function DebugCard() {
  const [debugResult, setDebugResult] = useState<DebugResult | null>(null);
  const [debugging, setDebugging] = useState(false);

  const handleDebugTest = async () => {
    setDebugging(true);
    setDebugResult(null);

    try {
      const response = await fetch('/api/airtable-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Let the endpoint get configuration from environment variables
          // instead of sending empty strings
        }),
      });
      const result = await response.json();
      setDebugResult(result);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Debug test failed';
      setDebugResult({ success: false, error: errorMessage });
    } finally {
      setDebugging(false);
    }
  };

  return (
    <Card className="border-destructive">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-destructive text-sm">
          <Bug className="h-5 w-5" />
          Debug Airtable Configuration
        </CardTitle>
        <CardDescription className="text-xs">
          Having trouble with Airtable? Run this diagnostic test to identify the
          issue.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button
          disabled={debugging}
          onClick={handleDebugTest}
          variant="outline"
        >
          {debugging ? 'Testing...' : 'Run Debug Test'}
        </Button>

        {debugResult && (
          <Card>
            <CardContent className="p-4">
              <div
                className={`mb-2 font-medium ${
                  debugResult.success ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {debugResult.success ? '✅ Success' : '❌ Failed'}
              </div>

              {debugResult.error && (
                <Alert className="mb-4" variant="destructive">
                  <AlertDescription>
                    <strong>Error:</strong> {debugResult.error}
                  </AlertDescription>
                </Alert>
              )}

              {debugResult.suggestion && (
                <Alert className="mb-4">
                  <AlertDescription>
                    <strong>Suggestion:</strong> {debugResult.suggestion}
                  </AlertDescription>
                </Alert>
              )}

              {debugResult.availableBases && (
                <div className="mb-4 text-xs">
                  <strong>Available Bases:</strong>
                  <ul className="mt-2 space-y-1">
                    {debugResult.availableBases.map(
                      (base: { id: string; name: string }) => (
                        <li className="flex justify-between" key={base.id}>
                          <span>{base.name}</span>
                          <code className="text-xs">{base.id}</code>
                        </li>
                      )
                    )}
                  </ul>
                </div>
              )}

              {debugResult.availableTables && (
                <div className="mb-4 text-xs">
                  <strong>Available Tables in Base:</strong>
                  <ul className="mt-2 space-y-1">
                    {debugResult.availableTables.map((table: string) => (
                      <li key={table}>
                        <Badge variant="outline">{table}</Badge>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {debugResult.success && (
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <strong>Base:</strong>
                    <span>{debugResult.baseName}</span>
                  </div>
                  <div className="flex justify-between">
                    <strong>Table:</strong>
                    <span>{debugResult.tableName}</span>
                  </div>
                  <div className="flex justify-between">
                    <strong>Fields:</strong>
                    <span>{debugResult.fieldsCount}</span>
                  </div>
                  {debugResult.sampleFields &&
                    debugResult.sampleFields.length > 0 && (
                      <div>
                        <strong>Sample Fields:</strong>
                        <div className="mt-1 flex flex-wrap gap-1">
                          {debugResult.sampleFields.map((field) => (
                            <Badge
                              className="text-xs"
                              key={field}
                              variant="outline"
                            >
                              {field}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              )}

              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button className="text-xs" size="sm" variant="ghost">
                    Raw Debug Response
                    <Code className="ml-2 h-3 w-3" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <Card className="mt-2">
                    <CardContent className="max-h-40 overflow-y-auto p-4">
                      <pre className="whitespace-pre-wrap font-mono text-xs">
                        {JSON.stringify(debugResult, null, 2)}
                      </pre>
                    </CardContent>
                  </Card>
                </CollapsibleContent>
              </Collapsible>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
