"use client";

import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

/**
 * Demo component to showcase the toast notifications used in bulk processing
 * This demonstrates the different types of toasts users will see
 */
export function ToastDemo() {
  const demoLoadingToast = () => {
    const loadingToast = toast.loading("Processing 3 job(s)...", {
      description: "Starting AI extraction • Estimated time: ~15 sec",
    });

    // Simulate processing time
    setTimeout(() => {
      toast.dismiss(loadingToast);
      toast.success("✅ All 3 job(s) processed successfully!", {
        description:
          "AI extraction completed • Jobs are now ready for publishing",
        duration: 6000,
        action: {
          label: "View Jobs",
          onClick: () => alert("Would navigate to jobs table"),
        },
      });
    }, 3000);
  };

  const demoPartialSuccess = () => {
    toast.warning("⚠️ Processing completed with mixed results", {
      description:
        "2 successful, 1 failed out of 3 jobs • Check failed jobs for details",
      duration: 7000,
      action: {
        label: "Refresh",
        onClick: () => alert("Would refresh the jobs table"),
      },
    });
  };

  const demoAllFailed = () => {
    toast.error("❌ All 3 job(s) failed to process", {
      description:
        "No jobs were successfully processed • Check job details and try again",
      duration: 8000,
      action: {
        label: "Refresh",
        onClick: () => alert("Would refresh the jobs table"),
      },
    });
  };

  const demoErrorDetails = () => {
    toast.error("Processing errors detected", {
      description:
        "• Invalid UUID format for job ID\n• Network timeout during extraction\n... and 1 more",
      duration: 8000,
    });
  };

  const demoOtherOperations = () => {
    // Demo other bulk operations
    setTimeout(() => {
      toast.success("✅ Deleted 5 job(s)", {
        description: "Jobs have been permanently removed",
        duration: 4000,
      });
    }, 500);

    setTimeout(() => {
      toast.success('✅ Updated 3 job(s) to "active"', {
        description: "Job status has been updated successfully",
        duration: 4000,
      });
    }, 1500);

    setTimeout(() => {
      toast.success("✅ Pushed 7 job(s) to Airtable", {
        description: "Jobs have been successfully synced to Airtable",
        duration: 4000,
      });
    }, 2500);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>🍞 Toast Notifications Demo</CardTitle>
        <CardDescription>
          Preview the live toast notifications used in bulk job processing
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-3">
          <Button onClick={demoLoadingToast} variant="default">
            Loading → Success
          </Button>
          <Button onClick={demoPartialSuccess} variant="outline">
            Partial Success
          </Button>
          <Button onClick={demoAllFailed} variant="destructive">
            All Failed
          </Button>
          <Button onClick={demoErrorDetails} variant="secondary">
            Error Details
          </Button>
        </div>

        <div className="pt-2">
          <Button
            onClick={demoOtherOperations}
            variant="outline"
            className="w-full"
          >
            Demo Other Operations (Sequence)
          </Button>
        </div>

        <div className="text-sm text-muted-foreground pt-4 border-t">
          <p>
            <strong>Enhanced Features:</strong>
          </p>
          <ul className="list-disc list-inside space-y-1 mt-2">
            <li>Loading states with estimated processing time</li>
            <li>Success/warning/error states with rich visual feedback</li>
            <li>Detailed descriptions with actionable information</li>
            <li>Action buttons for quick navigation (View Jobs, Refresh)</li>
            <li>Smart duration based on message importance</li>
            <li>Error details with sample messages for debugging</li>
            <li>Consistent styling with Sonner toast library</li>
            <li>Contextual emojis for better visual recognition</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
