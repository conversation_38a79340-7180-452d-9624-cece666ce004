'use client';

import { RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ConnectedSourcesHealthCard } from './connected-sources-health-card';
import { DeduplicationHealthCard } from './deduplication-health-card';
import { EnvironmentHealthCard } from './environment-health-card';
import { JobStatsCard } from './job-stats-card';
import { LogsHealthCard } from './logs-health-card';
import { MonitoringHealthCard } from './monitoring-health-card';
import { SourcesHealthCard } from './sources-health-card';
import { SystemHealthCard } from './system-health-card';

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  checks: {
    database: {
      status: 'healthy' | 'error' | 'unknown';
      latency: number;
      error?: string;
    };
    redis: {
      status: 'healthy' | 'error' | 'unknown';
      latency: number;
      error?: string;
    };
    qstash: {
      status: 'healthy' | 'error' | 'unknown';
      latency: number;
      error?: string;
    };
    apify: {
      status: 'healthy' | 'error' | 'unknown';
      latency: number;
      error?: string;
    };
    slack: {
      status: 'healthy' | 'error' | 'unknown';
      latency: number;
      error?: string;
    };
    uptime: number;
  };
}

interface SourcesHealth {
  success: boolean;
  data?: {
    sources: Array<{
      id: string;
      name: string;
      enabled: boolean;
      stats: {
        success_rate: number;
        error_count_24h: number;
        last_success: string;
        last_error: string;
      };
    }>;
  };
}

interface ConnectedSourcesHealth {
  success: boolean;
  sources?: Array<{
    id: string;
    name: string;
    type: 'API' | 'RSS' | 'Manual' | 'Generator';
    endpoint: string;
    enabled: boolean;
    description: string;
    config: Record<string, unknown>;
    created_at: string;
    updated_at: string;
    stats: {
      last_fetch_at: string | null;
      total_jobs_fetched: number;
      jobs_fetched_today: number;
      success_rate: number;
      avg_response_time_ms: number;
      error_count_24h: number;
      last_error: string | null;
      last_error_at: string | null;
      rate_limit_info: Record<string, unknown>;
    };
  }>;
  count?: number;
}

interface JobStats {
  success: boolean;
  data?: {
    total: number;
    completed: number;
    failed: number;
    monitoring: {
      active: number;
      pending: number;
      error: number;
    };
  };
}

interface MonitoringData {
  success: boolean;
  data?: Record<string, unknown>;
}

interface LogsData {
  success: boolean;
  data?: Record<string, unknown>;
}

interface DeduplicationData {
  success: boolean;
  data?: {
    redis: {
      status: 'healthy' | 'error' | 'unknown';
      latency: number;
      connection: boolean;
      error?: string;
    };
    statistics: {
      total_checks_24h: number;
      duplicates_found_24h: number;
      cost_savings_24h: number;
      duplicate_rate_24h: number;
      level_breakdown: {
        level1: number;
        level2: number;
        level3: number;
      };
      performance: {
        avg_check_time_ms: number;
        fastest_check_ms: number;
        slowest_check_ms: number;
      };
    };
    alerts: {
      high_duplicate_rate: boolean;
      redis_connection_issues: boolean;
      slow_performance: boolean;
    };
  };
}

interface HealthData {
  systemHealth: SystemHealth | undefined;
  sourcesHealth: SourcesHealth | undefined;
  connectedSourcesHealth: ConnectedSourcesHealth | undefined;
  jobStats: JobStats | undefined;
  monitoringData: MonitoringData | undefined;
  logsData: LogsData | undefined;
  deduplicationData: DeduplicationData | undefined;
  lastUpdated: string;
}

export function HealthDashboard() {
  const [data, setData] = useState<HealthData>({
    systemHealth: undefined,
    sourcesHealth: undefined,
    connectedSourcesHealth: undefined,
    jobStats: undefined,
    monitoringData: undefined,
    logsData: undefined,
    deduplicationData: undefined,
    lastUpdated: new Date().toISOString(),
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchHealthData = useCallback(async () => {
    try {
      const [systemRes, sourcesRes, jobsRes, dedupRes] = await Promise.all([
        fetch('/api/health'),
        fetch('/api/sources'),
        fetch('/api/job-stats'),
        fetch('/api/dedup-stats'),
      ]);

      const systemHealth = await systemRes.json();
      const sourcesHealth = await sourcesRes.json();
      const jobStats = await jobsRes.json();
      const deduplicationData = await dedupRes.json();

      setData({
        systemHealth,
        sourcesHealth,
        connectedSourcesHealth: sourcesHealth, // Use the same sources data for the detailed view
        jobStats,
        monitoringData: { success: false },
        logsData: { success: false },
        deduplicationData,
        lastUpdated: new Date().toISOString(),
      });
    } catch (_error) {
      // Handle error silently for now
      setData((prev) => ({
        ...prev,
        lastUpdated: new Date().toISOString(),
      }));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchHealthData();
  }, [fetchHealthData]);

  useEffect(() => {
    fetchHealthData();
    const interval = setInterval(fetchHealthData, 30_000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [fetchHealthData]);

  if (loading) {
    const loadingCards = [
      'system',
      'sources',
      'jobs',
      'deduplication',
      'monitoring',
      'environment',
      'logs',
    ];
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {loadingCards.map((cardType) => (
            <Card key={`loading-${cardType}`}>
              <CardHeader className="pb-2">
                <div className="h-4 w-24 animate-pulse rounded bg-muted" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-8 w-16 animate-pulse rounded bg-muted" />
                  <div className="h-4 w-32 animate-pulse rounded bg-muted" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-3xl">System Health</h1>
          <p className="text-muted-foreground">
            Monitor the health and performance of all Bordfeed services
          </p>
        </div>
        <Button
          disabled={refreshing}
          onClick={handleRefresh}
          size="sm"
          variant="outline"
        >
          <RefreshCw
            className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <SystemHealthCard data={data.systemHealth} />
        <SourcesHealthCard data={data.sourcesHealth} />
        <JobStatsCard data={data.jobStats} />
        <DeduplicationHealthCard data={data.deduplicationData} />
        <MonitoringHealthCard data={data.monitoringData} />
        <EnvironmentHealthCard />
        <LogsHealthCard data={data.logsData?.data as Record<string, number>} />
        <ConnectedSourcesHealthCard data={data.connectedSourcesHealth} />
      </div>

      <div className="text-muted-foreground text-xs">
        Last updated: {new Date(data.lastUpdated).toLocaleString()}
      </div>
    </div>
  );
}
