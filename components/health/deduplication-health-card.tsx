'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>cle,
  Clock,
  Shield,
  TrendingDown,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface DeduplicationData {
  success: boolean;
  data?: {
    redis: {
      status: 'healthy' | 'error' | 'unknown';
      latency: number;
      connection: boolean;
      error?: string;
    };
    statistics: {
      total_checks_24h: number;
      duplicates_found_24h: number;
      cost_savings_24h: number;
      duplicate_rate_24h: number;
      level_breakdown: {
        level1: number; // Apply URL matches
        level2: number; // Source URL + content matches
        level3: number; // Content similarity matches
      };
      performance: {
        avg_check_time_ms: number;
        fastest_check_ms: number;
        slowest_check_ms: number;
      };
    };
    alerts: {
      high_duplicate_rate: boolean;
      redis_connection_issues: boolean;
      slow_performance: boolean;
    };
  };
}

interface DeduplicationHealthCardProps {
  data?: DeduplicationData;
}

function LoadingCard() {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Shield className="h-4 w-4" />
          Deduplication
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">--</span>
            <Badge variant="secondary">Loading</Badge>
          </div>
          <div className="text-muted-foreground text-xs">
            Loading deduplication metrics...
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function getRedisStatusIcon(status: string) {
  switch (status) {
    case 'healthy':
      return <CheckCircle className="h-3 w-3 text-green-500" />;
    case 'error':
      return <AlertTriangle className="h-3 w-3 text-red-500" />;
    default:
      return <Clock className="h-3 w-3 text-gray-500" />;
  }
}

function getPerformanceStatus(
  stats: { performance: { avg_check_time_ms: number } } | undefined
) {
  if (!stats) {
    return 'unknown';
  }
  if (stats.performance.avg_check_time_ms > 100) {
    return 'slow';
  }
  if (stats.performance.avg_check_time_ms > 50) {
    return 'moderate';
  }
  return 'fast';
}

function formatCostSavings(savings: number) {
  if (savings === 0) {
    return '0';
  }
  if (savings < 1) {
    return `$${savings.toFixed(2)}`;
  }
  return `$${savings.toFixed(0)}`;
}

function getPerformanceBadgeVariant(status: string) {
  if (status === 'fast') {
    return 'default';
  }
  if (status === 'moderate') {
    return 'secondary';
  }
  if (status === 'unknown') {
    return 'outline';
  }
  return 'destructive';
}

export function DeduplicationHealthCard({
  data,
}: DeduplicationHealthCardProps) {
  if (!data?.success) {
    return <LoadingCard />;
  }

  const stats = data.data?.statistics;
  const redis = data.data?.redis;
  const alerts = data.data?.alerts;
  const duplicateRate = stats?.duplicate_rate_24h || 0;
  const totalChecks = stats?.total_checks_24h || 0;
  const duplicatesFound = stats?.duplicates_found_24h || 0;
  const performanceStatus = getPerformanceStatus(stats);

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Shield className="h-4 w-4" />
          Deduplication
          {alerts?.high_duplicate_rate && (
            <Badge className="ml-auto" variant="destructive">
              High Rate
            </Badge>
          )}
          {alerts?.redis_connection_issues && (
            <Badge className="ml-auto" variant="outline">
              Redis Issue
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Key Metrics */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">
              {duplicateRate.toFixed(1)}%
            </span>
            <TrendingDown className="h-4 w-4 text-green-500" />
          </div>
          <p className="text-muted-foreground text-xs">Duplicate Rate (24h)</p>
        </div>

        {/* Performance Stats */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div>
            <div className="font-medium">{totalChecks.toLocaleString()}</div>
            <div className="text-muted-foreground">Checks (24h)</div>
          </div>
          <div>
            <div className="font-medium">
              {duplicatesFound.toLocaleString()}
            </div>
            <div className="text-muted-foreground">Duplicates</div>
          </div>
          <div>
            <div className="font-medium">
              {formatCostSavings(stats?.cost_savings_24h || 0)}
            </div>
            <div className="text-muted-foreground">Cost Saved</div>
          </div>
          <div>
            <div className="font-medium">
              {stats?.performance.avg_check_time_ms || 0}ms
            </div>
            <div className="text-muted-foreground">Avg Time</div>
          </div>
        </div>

        {/* Level Breakdown */}
        <div className="space-y-2">
          <div className="font-medium text-xs">Detection Layers</div>
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span>Level 1 (Apply URL)</span>
              <span className="font-medium text-blue-600">
                {stats?.level_breakdown.level1 || 0}
              </span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span>Level 2 (Source+Content)</span>
              <span className="font-medium text-green-600">
                {stats?.level_breakdown.level2 || 0}
              </span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span>Level 3 (Similarity)</span>
              <span className="font-medium text-orange-600">
                {stats?.level_breakdown.level3 || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Redis Status */}
        <div className="border-t pt-2">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-2">
              {getRedisStatusIcon(redis?.status || 'unknown')}
              <span>Redis Connection</span>
            </div>
            <span className="text-muted-foreground">
              {redis?.latency || 0}ms
            </span>
          </div>
          {redis?.error && (
            <div className="mt-1 text-red-500 text-xs">{redis.error}</div>
          )}
        </div>

        {/* Performance Indicator */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span>Performance</span>
            <Badge
              className="text-xs"
              variant={getPerformanceBadgeVariant(performanceStatus)}
            >
              {performanceStatus.toUpperCase()}
            </Badge>
          </div>
          <Progress
            className="h-1"
            value={Math.min(
              100,
              Math.max(0, 100 - (stats?.performance.avg_check_time_ms || 0))
            )}
          />
          <div className="flex justify-between text-muted-foreground text-xs">
            <span>{stats?.performance.fastest_check_ms || 0}ms fastest</span>
            <span>{stats?.performance.slowest_check_ms || 0}ms slowest</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
