'use client';

import { AlertCircle, Briefcase, TrendingUp } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface JobStatsData {
  success: boolean;
  data?: {
    total: number;
    completed: number;
    failed: number;
    monitoring: {
      active: number;
      pending: number;
      error: number;
    };
  };
}

interface JobStatsCardProps {
  data?: JobStatsData;
}

export function JobStatsCard({ data }: JobStatsCardProps) {
  if (!(data?.success && data.data)) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="font-medium text-sm">Job Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  const stats = data.data;
  const completionRate =
    stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Briefcase className="h-4 w-4" />
          Job Statistics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">{stats.total}</span>
            <Badge variant="secondary">Total Jobs</Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Completion Rate</span>
              <span className="font-medium">{completionRate.toFixed(1)}%</span>
            </div>
            <Progress className="h-2" value={completionRate} />
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-muted-foreground">Completed:</span>
              <span className="font-medium">{stats.completed}</span>
            </div>
            <div className="flex items-center gap-2">
              <AlertCircle className="h-3 w-3 text-red-500" />
              <span className="text-muted-foreground">Failed:</span>
              <span className="font-medium">{stats.failed}</span>
            </div>
          </div>

          <div className="border-t pt-2">
            <div className="mb-2 font-medium text-xs">Monitoring Status</div>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="text-center">
                <div className="font-medium">{stats.monitoring.active}</div>
                <div className="text-muted-foreground">Active</div>
              </div>
              <div className="text-center">
                <div className="font-medium">{stats.monitoring.pending}</div>
                <div className="text-muted-foreground">Pending</div>
              </div>
              <div className="text-center">
                <div className="font-medium">{stats.monitoring.error}</div>
                <div className="text-muted-foreground">Error</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
