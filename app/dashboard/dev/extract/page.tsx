'use client';

import { useEffect, useState } from 'react';
import { ExtractStats } from '@/components/extract/extract-stats';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useJobExtraction } from '@/lib/hooks/use-job-extraction';
import type { JobData } from '@/lib/types';

type StepStatus = 'idle' | 'loading' | 'success' | 'error';

// Helper function to get step button properties
const getStepButtonProps = (status: StepStatus, isDisabled = false) => {
  const baseProps = {
    disabled: isDisabled,
    className: 'flex-1',
  };

  if (status === 'loading') {
    return { ...baseProps, loadingState: 'loading' as const };
  }

  return baseProps;
};

// Helper function to get step icons
const getStepIcon = (status: StepStatus) => {
  switch (status) {
    case 'success':
      return '✅';
    case 'error':
      return '❌';
    case 'loading':
      return '⏳';
    default:
      return '🔘';
  }
};

export default function DevExtractPage() {
  const [content, setContent] = useState('');

  // Step statuses - only keeping AI processing and Airtable sync
  const [processStatus, setProcessStatus] = useState<StepStatus>('idle');
  const [syncStatus, setSyncStatus] = useState<StepStatus>('idle');

  // Data states
  const [processedJob, setProcessedJob] = useState<JobData | null>(null);
  const [error, setError] = useState<string>('');

  // Use the job extraction hook
  const {
    extractJob,
    result,
    metadata,
    error: extractError,
  } = useJobExtraction();

  // Handle AI processing result
  useEffect(() => {
    if (result && processStatus === 'loading') {
      setProcessedJob(result);
      setProcessStatus('success');
    }
  }, [result, processStatus]);

  // Update error state from extraction hook
  useEffect(() => {
    if (extractError) {
      setError(extractError);
      setProcessStatus('error');
    }
  }, [extractError]);

  // Step 1: AI PROCESS manual content
  const handleProcess = async () => {
    if (!content.trim()) {
      setError('Please enter job content to process.');
      return;
    }

    setProcessStatus('loading');
    setError('');

    try {
      // Use the extraction hook to process the job content directly
      await extractJob(content);

      // Note: result will be handled by useEffect when available
    } catch (err) {
      setError(err instanceof Error ? err.message : 'AI processing failed');
      setProcessStatus('error');
    }
  };

  // Step 2: AIRTABLE SYNC
  const handleSync = async () => {
    if (!processedJob) {
      setError('Please process job data first before syncing to Airtable.');
      return;
    }

    setSyncStatus('loading');
    setError('');

    try {
      const response = await fetch('/api/airtable-send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobData: processedJob }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sync to Airtable');
      }

      setSyncStatus('success');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Airtable sync failed');
      setSyncStatus('error');
    }
  };

  const handleReset = () => {
    setContent('');
    setProcessedJob(null);
    setError('');
    setProcessStatus('idle');
    setSyncStatus('idle');
  };

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="font-bold text-3xl tracking-tight">
          AI Job Extraction Testing
        </h2>
      </div>

      <div className="text-muted-foreground">
        Development tool for testing AI-powered job data extraction on manual
        job posting content. Useful for debugging extraction accuracy and
        testing new job sources.
      </div>

      {/* Statistics */}
      <ExtractStats />

      <div className="space-y-6">
        {/* Manual Input */}
        <div>
          <label
            className="mb-2 block font-medium text-gray-700 text-sm"
            htmlFor="job-content"
          >
            Job Post Content
          </label>
          <Textarea
            id="job-content"
            onChange={(e) => setContent(e.target.value)}
            placeholder="Paste your job posting content here..."
            rows={12}
            value={content}
          />
          <p className="mt-1 text-gray-500 text-xs">
            Paste the full job posting text to test AI extraction accuracy
          </p>
        </div>

        {/* 2-Step Control Buttons */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">AI Extraction Workflow</h3>

          <div className="grid grid-cols-2 gap-3">
            {/* Step 1: AI PROCESS */}
            <Button
              {...getStepButtonProps(processStatus, !content.trim())}
              onClick={handleProcess}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleProcess();
                }
              }}
              type="button"
            >
              {getStepIcon(processStatus)} Step 1: AI EXTRACT
            </Button>

            {/* Step 2: AIRTABLE SYNC */}
            <Button
              {...getStepButtonProps(syncStatus, processStatus !== 'success')}
              onClick={handleSync}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleSync();
                }
              }}
              type="button"
            >
              {getStepIcon(syncStatus)} Step 2: AIRTABLE SYNC
            </Button>
          </div>

          {/* Reset Button */}
          <Button
            className="w-full"
            onClick={handleReset}
            type="button"
            variant="outline"
          >
            🔄 Reset All
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Processing Results */}
        {processedJob && (
          <div className="rounded-md bg-green-50 p-4">
            <h3 className="mb-2 font-medium text-green-800">
              ✅ AI Extraction Complete
            </h3>
            <div className="space-y-2 text-green-700 text-sm">
              <p>
                <strong>Title:</strong> {processedJob.title}
              </p>
              <p>
                <strong>Company:</strong> {processedJob.company}
              </p>
              <p>
                <strong>Type:</strong> {processedJob.type}
              </p>
              {processedJob.salary_min && processedJob.salary_max && (
                <p>
                  <strong>Salary:</strong> {processedJob.salary_currency}
                  {processedJob.salary_min?.toLocaleString()} -{' '}
                  {processedJob.salary_currency}
                  {processedJob.salary_max?.toLocaleString()}{' '}
                  {processedJob.salary_unit}
                </p>
              )}
              <p>
                <strong>Workplace:</strong> {processedJob.workplace_type}
                {processedJob.remote_region &&
                  ` (${processedJob.remote_region})`}
              </p>
              <p>
                <strong>Apply Method:</strong> {processedJob.apply_method}
              </p>
              {metadata && (
                <div className="mt-3 rounded bg-green-100 p-2">
                  <p className="font-medium text-green-800 text-xs">
                    Metadata:
                  </p>
                  <p>
                    Response time: {(metadata.duration / 1000).toFixed(2)}s •
                    Cost: ${metadata.cost.total.toFixed(6)} • Tokens:{' '}
                    {metadata.usage.totalTokens.toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Sync Success */}
        {syncStatus === 'success' && (
          <div className="rounded-md bg-blue-50 p-4">
            <h3 className="mb-2 font-medium text-blue-800">
              ✅ Airtable Sync Complete
            </h3>
            <p className="text-blue-700 text-sm">
              Job data has been successfully sent to your configured Airtable
              base.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
