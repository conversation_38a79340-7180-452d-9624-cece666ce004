'use client';

import { useRouter } from 'next/navigation';
import type React from 'react';
import { useEffect, use as usePromise, useState } from 'react';
import type { DatabaseJob } from '@/lib/storage';
import { logger } from '@/lib/utils';

interface JobDetailPageProps {
  params: Promise<{ id: string }>;
}

const JobDetailPage: React.FC<JobDetailPageProps> = ({ params }) => {
  const router = useRouter();
  const [job, setJob] = useState<DatabaseJob | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<DatabaseJob>>({});
  const [saving, setSaving] = useState(false);

  const resolvedParams = usePromise(params);
  const jobId = resolvedParams.id;

  useEffect(() => {
    const fetchJob = async () => {
      try {
        const response = await fetch(`/api/jobs/${jobId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const data = await response.json();
        setJob(data.job);
        setEditForm(data.job);

        logger.info('Job loaded successfully', { jobId });
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to load job';
        setError(errorMessage);
        logger.error('Failed to load job:', errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchJob();
  }, [jobId]);

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form to current job data when canceling
      setEditForm(job || {});
    }
    setIsEditing(!isEditing);
  };

  const handleSave = async () => {
    if (!job) {
      return;
    }

    setSaving(true);
    try {
      const response = await fetch(`/api/jobs/${jobId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update job');
      }

      const data = await response.json();
      setJob(data.job);
      setEditForm(data.job);
      setIsEditing(false);

      logger.info('Job updated successfully', { jobId });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update job';
      setError(errorMessage);
      logger.error('Failed to update job:', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (
    field: keyof DatabaseJob,
    value: string | boolean | null
  ) => {
    setEditForm((prev: Partial<DatabaseJob>) => ({
      ...prev,
      [field]: value,
    }));
  };

  const formatValue = (value: unknown): string => {
    if (value === null || value === undefined) {
      return 'N/A';
    }
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const renderField = (key: string, _label: string, value: unknown) => {
    const isEditMode =
      isEditing && key !== 'id' && key !== 'created_at' && key !== 'updated_at';

    if (isEditMode) {
      const editValue = editForm[key as keyof DatabaseJob];

      if (typeof value === 'boolean') {
        return (
          <select
            className="w-full rounded border border-gray-300 px-2 py-1 text-xs"
            onChange={(e) =>
              handleInputChange(
                key as keyof DatabaseJob,
                e.target.value === 'true'
              )
            }
            value={String(editValue)}
          >
            <option value="true">Yes</option>
            <option value="false">No</option>
          </select>
        );
      }

      if (
        key === 'description' ||
        key === 'responsibilities' ||
        key === 'benefits'
      ) {
        return (
          <textarea
            className="w-full rounded border border-gray-300 px-2 py-1 text-xs"
            onChange={(e) =>
              handleInputChange(key as keyof DatabaseJob, e.target.value)
            }
            rows={4}
            value={String(editValue || '')}
          />
        );
      }

      return (
        <input
          className="w-full rounded border border-gray-300 px-2 py-1 text-xs"
          onChange={(e) =>
            handleInputChange(key as keyof DatabaseJob, e.target.value)
          }
          type="text"
          value={String(editValue || '')}
        />
      );
    }

    return (
      <div className="whitespace-pre-wrap break-words rounded bg-gray-50 p-2 text-xs">
        {formatValue(value)}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-gray-500">Loading job details...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <div className="rounded-lg bg-red-50 p-6">
            <div className="text-red-800">Error: {error}</div>
            <div className="mt-4 space-x-3">
              <button
                className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
                onClick={() => window.location.reload()}
                type="button"
              >
                Retry
              </button>
              <button
                className="rounded border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
                onClick={() => router.back()}
                type="button"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <div className="text-center">
            <div className="text-gray-500">Job not found</div>
            <button
              className="mt-4 rounded border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
              onClick={() => router.back()}
              type="button"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-4xl px-4 py-8">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <div>
            <button
              className="mb-2 text-blue-600 hover:text-blue-800"
              onClick={() => router.back()}
              type="button"
            >
              ← Back to Jobs
            </button>
            <h1 className="font-bold text-2xl text-gray-900">{job.title}</h1>
            <p className="text-gray-600">
              {job.company} • Job ID: {job.id}
            </p>
          </div>
          <div className="space-x-3">
            {isEditing ? (
              <>
                <button
                  className="rounded border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
                  onClick={handleEditToggle}
                  type="button"
                >
                  Cancel
                </button>
                <button
                  className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50"
                  disabled={saving}
                  onClick={handleSave}
                  type="button"
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </>
            ) : (
              <button
                className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
                onClick={handleEditToggle}
                type="button"
              >
                Edit Job
              </button>
            )}
          </div>
        </div>

        {/* Job Details */}
        <div className="rounded-lg bg-white shadow">
          <div className="border-gray-200 border-b px-6 py-4">
            <h2 className="font-semibold text-gray-900 text-lg">Job Details</h2>
            {isEditing && (
              <p className="mt-1 text-gray-600 text-sm">
                Make changes below and click "Save Changes" to update the job.
              </p>
            )}
          </div>

          <div className="p-6">
            {/* Unified view of all fields */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Object.entries(job).map(([fieldKey, fieldVal]) => (
                <div key={fieldKey}>
                  <span className="block font-medium text-gray-700 text-xs">
                    {fieldKey}
                  </span>
                  {renderField(fieldKey, fieldKey, fieldVal as unknown)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetailPage;
