import { createServerClient } from '@/lib/supabase';

export const dynamic = 'force-dynamic';

interface LogRow {
  id: string;
  job_id: string | null;
  previous_status: string | null;
  new_status: string | null;
  checked_at: string | null;
  duration_millis: number | null;
  total_tokens: number | null;
  model: string | null;
  head_status: number | null;
  head_ok: boolean | null;
  decision_layer: string | null;
}

const PAGE_SIZE = 20;

async function fetchLogs({
  cursor,
  jobId,
}: {
  cursor?: string;
  jobId?: string;
}) {
  const supabase = await createServerClient();

  const query = supabase
    .from('job_monitor_logs')
    .select('*')
    .order('checked_at', { ascending: false })
    .limit(PAGE_SIZE + 1);

  if (jobId) {
    query.eq('job_id', jobId);
  }

  if (cursor) {
    query.lt('checked_at', cursor);
  }

  const { data, error } = await query;
  if (error) {
    throw error;
  }
  return data as LogRow[];
}

export default async function MonitorLogsPage({
  searchParams,
}: {
  searchParams: Promise<{ cursor?: string; jobId?: string }>;
}) {
  const { cursor, jobId } = await searchParams;
  const logs = await fetchLogs({ cursor, jobId });
  const hasMore = logs.length > PAGE_SIZE;
  const displayLogs = hasMore ? logs.slice(0, PAGE_SIZE) : logs;
  const nextCursor = hasMore ? displayLogs.at(-1)?.checked_at : undefined;

  return (
    <div className="mx-auto max-w-5xl p-8">
      <h1 className="mb-6 font-semibold text-2xl">📊 Monitor Logs</h1>
      <table className="min-w-full divide-y divide-gray-200 text-sm">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              Checked
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              Job ID
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              Prev
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              New
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              Duration (ms)
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              Tokens
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              Model
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              Decision
            </th>
            <th className="px-2 py-3 text-left font-medium text-gray-700">
              HTTP
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {displayLogs.map((log) => (
            <tr className="whitespace-nowrap" key={log.id}>
              <td className="px-2 py-2">
                {log.checked_at?.slice(0, 19).replace('T', ' ')}
              </td>
              <td className="px-2 py-2 text-blue-600">
                {log.job_id?.slice(0, 8)}
              </td>
              <td className="px-2 py-2">{log.previous_status || '-'}</td>
              <td className="px-2 py-2">{log.new_status}</td>
              <td className="px-2 py-2">{log.duration_millis ?? '-'}</td>
              <td className="px-2 py-2">{log.total_tokens ?? '-'}</td>
              <td className="px-2 py-2">{log.model ?? '-'}</td>
              <td className="px-2 py-2">{log.decision_layer ?? '-'}</td>
              <td className="px-2 py-2">
                {log.head_ok ? '✅' : '❌'} {log.head_status}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {hasMore && nextCursor && (
        <div className="mt-4 text-center">
          <a
            className="text-blue-600 hover:underline"
            href={`/dashboard/monitor?cursor=${encodeURIComponent(nextCursor)}`}
          >
            Next Page →
          </a>
        </div>
      )}
    </div>
  );
}
