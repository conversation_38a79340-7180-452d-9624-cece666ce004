import { Receiver } from '@upstash/qstash';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Get environment variables
    const currentSigningKey = process.env.QSTASH_CURRENT_SIGNING_KEY;
    const nextSigningKey = process.env.QSTASH_NEXT_SIGNING_KEY;

    // Get raw request data
    const body = await request.text();
    const signature = request.headers.get('Upstash-Signature');
    const url = request.url;

    if (!(currentSigningKey && nextSigningKey)) {
      return NextResponse.json(
        { error: 'Missing signing keys' },
        { status: 500 }
      );
    }

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing signature header' },
        { status: 401 }
      );
    }

    // Create receiver exactly as per documentation
    const receiver = new Receiver({
      currentSigningKey,
      nextSigningKey,
    });

    // Try signature verification
    let isValid = false;
    let verificationError: Error | null = null;

    try {
      isValid = await receiver.verify({
        body,
        signature,
        url,
      });
    } catch (error) {
      verificationError =
        error instanceof Error ? error : new Error(String(error));
    }

    // Try parsing the body
    let parsedBody: unknown = null;
    let parseError: Error | null = null;

    try {
      parsedBody = JSON.parse(body);
    } catch (error) {
      parseError = error instanceof Error ? error : new Error(String(error));
    }

    // Return comprehensive test results
    const testResult = {
      success: isValid,
      timestamp: new Date().toISOString(),
      verification: {
        isValid,
        error: verificationError
          ? {
              message:
                verificationError instanceof Error
                  ? verificationError.message
                  : String(verificationError),
              type:
                verificationError instanceof Error
                  ? verificationError.constructor.name
                  : typeof verificationError,
            }
          : null,
      },
      request: {
        hasSignature: !!signature,
        signatureLength: signature?.length || 0,
        bodyLength: body.length,
        url,
        headers: Object.fromEntries(request.headers.entries()),
      },
      parsing: {
        success: !!parsedBody,
        error: (() => {
          if (!parseError) {
            return null;
          }
          if (parseError instanceof Error) {
            return parseError.message;
          }
          return String(parseError);
        })(),
        parsedKeys: parsedBody ? Object.keys(parsedBody) : null,
      },
      environment: {
        hasCurrentKey: !!currentSigningKey,
        hasNextKey: !!nextSigningKey,
        nodeVersion: process.version,
        platform: process.platform,
      },
    };

    if (isValid) {
      return NextResponse.json(
        {
          message: '✅ Webhook verified successfully',
          testResult,
        },
        { status: 200 }
      );
    }
    return NextResponse.json(
      {
        message: '❌ Webhook verification failed',
        testResult,
      },
      { status: 401 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
