import { Receiver } from '@upstash/qstash';
import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { UPSTASH_CONFIG } from '@/lib/constants';
import { logger } from '@/lib/logger';

/**
 * QStash Webhook Callbacks Handler
 *
 * Receives success/failure callbacks from QStash after message processing
 * Provides end-to-end visibility into the job processing pipeline
 *
 * Flow:
 * 1. Webhook queues jobs via QStash with callback URLs
 * 2. QStash processes jobs via /api/pipeline-ingest
 * 3. QStash sends results to this callback endpoint
 * 4. We log/notify based on success/failure
 */

// Helper function to generate unique callback IDs
function generateCallbackId(): string {
  return `cb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Simplified callback schema for debugging
const CallbackSchema = z.object({
  status: z.number().optional(),
  responseBody: z.string().optional(),
  // Upstash sends createdAt as Unix millis (number); accept both number & ISO string for safety
  createdAt: z.union([z.number(), z.string()]).optional(),
  sourceMessageId: z.string().optional(),
  dlqId: z.string().optional(),
});

/**
 * Handles successful callback from QStash
 */
function handleSuccessCallback(
  callbackId: string,
  status: number,
  responseBody?: string,
  processingTime?: number
): void {
  logger.info(`✅ [${callbackId}] Success callback processed`, {
    status,
    processingTime,
    hasResponseBody: !!responseBody,
  });

  // Parse response body if present
  if (responseBody) {
    try {
      const decodedBody = Buffer.from(responseBody, 'base64').toString('utf-8');
      const parsedResponse = JSON.parse(decodedBody);

      logger.info(`📊 [${callbackId}] Pipeline response parsed`, {
        success: parsedResponse.success,
        processed: parsedResponse.processed || 0,
        duplicates: parsedResponse.duplicates || 0,
        errors: parsedResponse.errors || 0,
        batchId: parsedResponse.batchId,
      });
    } catch (parseError) {
      logger.warn(`⚠️ [${callbackId}] Could not parse response body`, {
        error:
          parseError instanceof Error ? parseError.message : 'Unknown error',
        responseBodyPreview: responseBody.substring(0, 200),
      });
    }
  }
}

/**
 * Handles failure callback from QStash (DLQ)
 */
function handleFailureCallback(
  callbackId: string,
  status: number,
  responseBody?: string,
  processingTime?: number
): void {
  logger.error(`❌ [${callbackId}] Failure callback received`, {
    status,
    processingTime,
    hasResponseBody: !!responseBody,
  });

  // Alert critical pipeline failures
  logger.critical(`💥 QStash pipeline failure - HTTP ${status}`, {
    callbackId,
    status,
    processingTime,
    responseBodyPreview: responseBody?.substring(0, 200) || 'none',
    timestamp: new Date().toISOString(),
  });
}

// Initialize QStash receiver for signature verification
const _receiver = new Receiver({
  currentSigningKey: UPSTASH_CONFIG.qstash.currentSigningKey,
  nextSigningKey: UPSTASH_CONFIG.qstash.nextSigningKey,
});

/**
 * Main webhook callback handler with comprehensive production debugging
 */
export async function POST(request: NextRequest) {
  try {
    // 🔍 DEBUG: Check environment variables
    const _envCheck = {
      QSTASH_TOKEN: !!UPSTASH_CONFIG.qstash.token,
      QSTASH_CURRENT_SIGNING_KEY: !!UPSTASH_CONFIG.qstash.currentSigningKey,
      QSTASH_NEXT_SIGNING_KEY: !!UPSTASH_CONFIG.qstash.nextSigningKey,
    };

    // 🔍 DEBUG: Get signature
    const signature = request.headers.get('upstash-signature');

    if (!signature) {
      return NextResponse.json({ error: 'Missing signature' }, { status: 403 });
    }
    const rawBody = await request.text();
    const receiver = new Receiver({
      currentSigningKey: UPSTASH_CONFIG.qstash.currentSigningKey,
      nextSigningKey: UPSTASH_CONFIG.qstash.nextSigningKey,
    });
    const isValid = await receiver.verify({
      body: rawBody,
      signature,
      url: request.url,
    });

    if (!isValid) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
    }
    const data = JSON.parse(rawBody) as unknown;
    const validatedData = CallbackSchema.parse(data);
    const callbackId = generateCallbackId();

    const { status = 500, responseBody, createdAt } = validatedData;

    // Normalize createdAt and calculate processing time
    const createdAtMillis = (() => {
      if (!createdAt) {
        return null;
      }
      if (typeof createdAt === 'number') {
        return createdAt;
      }
      return new Date(createdAt).getTime();
    })();

    const processingTime = createdAtMillis ? Date.now() - createdAtMillis : 0;

    if (status >= 200 && status < 300) {
      await handleSuccessCallback(
        callbackId,
        status,
        responseBody,
        processingTime
      );
    } else {
      await handleFailureCallback(
        callbackId,
        status,
        responseBody,
        processingTime
      );
    }
    return NextResponse.json({
      success: true,
      callbackId,
      status,
      processingTime,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    // Alert via Slack
    logger.critical('💥 Webhook callback critical failure', {
      error: errorMessage,
      stack: errorStack,
      url: request.url,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for debugging and health checking
 */
export function GET() {
  return NextResponse.json({
    message: '🔔 QStash webhook callbacks endpoint ready',
    version: 'v1.0.0',
    timestamp: new Date().toISOString(),
    test: 'GET endpoint working',
    environment: {
      hasQStashToken: !!UPSTASH_CONFIG.qstash.token,
      hasCurrentSigningKey: !!UPSTASH_CONFIG.qstash.currentSigningKey,
      hasNextSigningKey: !!UPSTASH_CONFIG.qstash.nextSigningKey,
      tokenLength: UPSTASH_CONFIG.qstash.token?.length || 0,
    },
    features: [
      '✅ Success callback handling',
      '✅ Failure callback (DLQ) handling',
      '✅ Pipeline step tracking',
      '✅ Slack notifications',
      '✅ Batch processing metrics',
      '✅ QStash signature verification',
      '🔍 Production debugging enabled',
    ],
  });
}
