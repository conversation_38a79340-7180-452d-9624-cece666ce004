import { type NextRequest, NextResponse } from 'next/server';
import {
  createJ<PERSON><PERSON>oard,
  deleteJobBoard,
  getJobBoardConfigs,
  type JobBoardConfig,
  updateJobBoard,
} from '@/lib/job-board-service';
import { logger } from '@/lib/utils';

// GET /api/job-boards - List all job board configurations
export async function GET() {
  try {
    const configs = await getJobBoardConfigs();

    return NextResponse.json({
      success: true,
      boards: configs,
      count: configs.length,
    });
  } catch (error) {
    logger.error('Error fetching job board configs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job board configurations' },
      { status: 500 }
    );
  }
}

// POST /api/job-boards - Create a new job board configuration
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Validate required fields
    const requiredFields = ['id', 'name', 'airtable', 'posting'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    if (!body.airtable.baseId) {
      return NextResponse.json(
        { error: 'Missing required field: airtable.baseId' },
        { status: 400 }
      );
    }

    const config: Omit<JobBoardConfig, 'lastPostedAt' | 'totalPosted'> = {
      id: body.id,
      name: body.name,
      description: body.description || '',
      enabled: body.enabled ?? true,
      airtable: {
        baseId: body.airtable.baseId,
        tableName: body.airtable.tableName || 'Jobs',
        pat: body.airtable.pat,
      },
      posting: {
        dailyLimit: body.posting.dailyLimit || 10,
        strategy: body.posting.strategy || 'newest_first',
        avoidRepostingDays: body.posting.avoidRepostingDays || 30,
        postingTimes: body.posting.postingTimes,
        timezone: body.posting.timezone,
      },
      filters: body.filters || {},
    };

    const success = await createJobBoard(config);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to create job board configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Job board '${config.name}' created successfully`,
      id: config.id,
    });
  } catch (error) {
    logger.error('Error creating job board config:', error);
    return NextResponse.json(
      { error: 'Failed to create job board configuration' },
      { status: 500 }
    );
  }
}

// PUT /api/job-boards?id=board-id - Update a job board configuration
export async function PUT(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const boardId = url.searchParams.get('id');

    if (!boardId) {
      return NextResponse.json(
        { error: 'Missing board ID parameter' },
        { status: 400 }
      );
    }

    const body = await req.json();
    const success = await updateJobBoard(boardId, body);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update job board configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Job board '${boardId}' updated successfully`,
    });
  } catch (error) {
    logger.error('Error updating job board config:', error);
    return NextResponse.json(
      { error: 'Failed to update job board configuration' },
      { status: 500 }
    );
  }
}

// DELETE /api/job-boards?id=board-id - Delete a job board configuration
export async function DELETE(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const boardId = url.searchParams.get('id');

    if (!boardId) {
      return NextResponse.json(
        { error: 'Missing board ID parameter' },
        { status: 400 }
      );
    }

    const success = await deleteJobBoard(boardId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete job board configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Job board '${boardId}' deleted successfully`,
    });
  } catch (error) {
    logger.error('Error deleting job board config:', error);
    return NextResponse.json(
      { error: 'Failed to delete job board configuration' },
      { status: 500 }
    );
  }
}
