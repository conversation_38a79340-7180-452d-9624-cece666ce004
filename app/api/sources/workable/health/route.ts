import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export const dynamic = 'force-dynamic';

/**
 * Workable Health Check Endpoint
 *
 * Tests the connection to Workable via Apify actor
 * Returns health status, response time, and actor information
 */

export async function GET() {
  const startTime = Date.now();

  try {
    // Check if required environment variables are configured
    const apifyToken = process.env.APIFY_TOKEN;
    const workableActorId = process.env.WORKABLE_ACTOR_ID;

    if (!apifyToken) {
      return NextResponse.json(
        {
          status: 'error',
          latency: Date.now() - startTime,
          error: 'Apify token not configured',
          source: 'workable',
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }

    if (!workableActorId) {
      return NextResponse.json(
        {
          status: 'error',
          latency: Date.now() - startTime,
          error: 'Workable actor ID not configured',
          source: 'workable',
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }

    // Test connection to Apify by fetching the actor details
    const response = await fetch(
      `https://api.apify.com/v2/acts/${workableActorId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
        // Add timeout to prevent hanging
        signal: AbortSignal.timeout(10_000), // 10 second timeout
      }
    );

    const latency = Date.now() - startTime;

    if (!response.ok) {
      if (response.status === 401) {
        return NextResponse.json(
          {
            status: 'error',
            latency,
            error: 'Invalid Apify token',
            source: 'workable',
            statusCode: response.status,
            timestamp: new Date().toISOString(),
          },
          { status: 500 }
        );
      }

      if (response.status === 404) {
        return NextResponse.json(
          {
            status: 'error',
            latency,
            error: 'Workable actor not found - check WORKABLE_ACTOR_ID',
            source: 'workable',
            statusCode: response.status,
            timestamp: new Date().toISOString(),
          },
          { status: 500 }
        );
      }

      return NextResponse.json(
        {
          status: 'error',
          latency,
          error: `Apify API returned ${response.status}: ${response.statusText}`,
          source: 'workable',
          statusCode: response.status,
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }

    // Parse response to validate actor details
    const actorData = await response.json();
    const isPublic = actorData.data.isPublic;
    const actorName = actorData.data.name;
    const username = actorData.data.username;

    // Also check recent runs to see if actor is functioning
    const runsResponse = await fetch(
      `https://api.apify.com/v2/acts/${workableActorId}/runs?limit=5`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10_000),
      }
    );

    let recentRunsInfo = { hasRuns: false, lastRunStatus: null, totalRuns: 0 };

    if (runsResponse.ok) {
      const runsData = await runsResponse.json();
      const runs = runsData.data.items || [];
      recentRunsInfo = {
        hasRuns: runs.length > 0,
        lastRunStatus: runs.length > 0 ? runs[0].status : null,
        totalRuns: runsData.data.total || 0,
      };
    }

    logger.info('Workable health check successful', {
      latency,
      actorName,
      username,
      isPublic,
      ...recentRunsInfo,
    });

    return NextResponse.json({
      status: 'healthy',
      latency,
      message: 'Workable actor connection successful',
      source: 'workable',
      details: {
        actorName,
        username,
        isPublic,
        actorId: workableActorId,
        apifyUrl: `https://console.apify.com/actors/${workableActorId}`,
        ...recentRunsInfo,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const latency = Date.now() - startTime;

    logger.error('Workable health check failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      latency,
    });

    return NextResponse.json(
      {
        status: 'error',
        latency,
        error:
          error instanceof Error ? error.message : 'Apify connection failed',
        source: 'workable',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
