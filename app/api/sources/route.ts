import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';
import { logger } from '@/lib/utils';

export const dynamic = 'force-dynamic';

/**
 * Apify-Connected Sources API
 *
 * Shows only the actual Apify actors configured for job data ingestion
 * Fetches real-time data directly from Apify API
 */

interface ApifyActor {
  id: string;
  name: string;
  username: string;
  description?: string;
  createdAt: string;
  modifiedAt: string;
  stats: {
    totalRuns: number;
  };
  example?: {
    runTimeMillis: number;
    memoryMbytes: number;
  };
}

interface ApifyActorRun {
  id: string;
  actId: string;
  status: string;
  statusMessage?: string;
  startedAt: string;
  finishedAt?: string;
  buildId?: string;
  defaultDatasetId?: string;
  defaultKeyValueStoreId?: string;
  usageTotalUsd?: number;
  usage?: {
    ACTOR_COMPUTE_UNITS: number;
    DATASET_WRITES: number;
    KEY_VALUE_STORE_WRITES: number;
    REQUEST_QUEUE_REQUESTS: number;
  };
}

// Configured Apify actors for job data ingestion
const CONFIGURED_ACTORS = [
  {
    id: 'workable',
    name: 'Workable XML Feed',
    description:
      'Global job feed from Workable via Apify actor (1000+ jobs/day)',
    envVar: 'WORKABLE_ACTOR_ID',
    endpoint: 'https://www.workable.com/boards/workable.xml',
  },
  {
    id: 'wwr-rss',
    name: 'WeWorkRemotely RSS',
    description: 'Remote job feed from WeWorkRemotely via Apify actor',
    envVar: 'WWR_ACTOR_ID',
    endpoint: 'https://weworkremotely.com/remote-jobs.rss',
  },
  {
    id: 'jobdata-api',
    name: 'JobDataAPI',
    description: 'Professional job data API via Apify actor',
    envVar: 'JOBDATAAPI_ACTOR_ID',
    endpoint: 'https://jobdataapi.com/api/jobs/',
  },
] as const;

/**
 * Fetch job source stats from database for a given source ID
 */
async function fetchJobSourceStats(sourceId: string) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_source_stats')
      .select('*')
      .eq('source_id', sourceId)
      .single();

    if (error || !data) {
      // Return default stats if not found
      return {
        total_jobs_fetched: 0,
        jobs_fetched_today: 0,
        avg_response_time_ms: 0,
        error_count_24h: 0,
        last_error_at: null,
        success_rate: 1.0,
      };
    }

    return {
      total_jobs_fetched: data.total_jobs_fetched || 0,
      jobs_fetched_today: data.jobs_fetched_today || 0,
      avg_response_time_ms: data.avg_response_time_ms || 0,
      error_count_24h: data.error_count_24h || 0,
      last_error_at: data.last_error_at,
      last_fetch_at: data.last_fetch_at,
      rate_limit_info: data.rate_limit_info || {},
    };
  } catch (error) {
    logger.error(`Failed to fetch job source stats for ${sourceId}:`, error);
    return {
      total_jobs_fetched: 0,
      jobs_fetched_today: 0,
      avg_response_time_ms: 0,
      error_count_24h: 0,
      last_error_at: null,
      success_rate: 1.0,
      rate_limit_info: {},
    };
  }
}

export async function GET() {
  try {
    const apifyToken = process.env.APIFY_TOKEN;

    if (!apifyToken) {
      logger.error('APIFY_TOKEN environment variable not set');
      return NextResponse.json(
        { error: 'Apify token not configured' },
        { status: 500 }
      );
    }

    logger.info('🔍 Fetching Apify actor data for configured sources');

    // Fetch data for all configured actors
    const sourcesWithStats = await Promise.all(
      CONFIGURED_ACTORS.map(async (actorConfig) => {
        const actorId = process.env[actorConfig.envVar];

        if (!actorId) {
          logger.warn(`${actorConfig.envVar} not configured`);
          return {
            id: actorConfig.id,
            name: actorConfig.name,
            description: `${actorConfig.description} (Not Configured)`,
            enabled: false,
            actor_id: '',
            endpoint: actorConfig.endpoint,
            stats: {
              // Job-specific metrics (defaults for unconfigured)
              total_jobs_fetched: 0,
              jobs_fetched_today: 0,
              avg_response_time_ms: 0,
              error_count_24h: 0,
              last_error_at: null,
              rate_limit_info: {},

              // General metrics
              last_fetch_at: null,
              success_rate: 0,
              last_error: 'Actor ID not configured',

              // Apify-specific metrics
              total_runs: 0,
              last_run_status: null,
            },
            schedule: {
              id: null,
              name: null,
              title: null,
              cronExpression: null,
              timezone: null,
              isEnabled: false,
              nextRunAt: null,
              lastRunAt: null,
              apifyUrl: null,
            },
            config: {
              apify_actor_url: '',
            },
          };
        }

        try {
          // Fetch actor details, recent runs, and schedule in parallel
          const [actorData, runsData, scheduleData] = await Promise.all([
            fetchActorDetails(apifyToken, actorId),
            fetchActorRuns(apifyToken, actorId, 10), // Last 10 runs
            fetchActorSchedule(apifyToken, actorId),
          ]);

          const stats = calculateActorStats(runsData);
          const latestRun = runsData[0];

          // Fetch job source stats from database
          const jobSourceStats = await fetchJobSourceStats(actorConfig.id);

          return {
            id: actorConfig.id,
            name: actorConfig.name,
            description: actorData?.description || actorConfig.description,
            enabled: true, // If configured, it's enabled
            actor_id: actorId,
            endpoint: actorConfig.endpoint,
            stats: {
              // Job-specific metrics from database
              total_jobs_fetched: jobSourceStats.total_jobs_fetched,
              jobs_fetched_today: jobSourceStats.jobs_fetched_today,
              avg_response_time_ms: jobSourceStats.avg_response_time_ms,
              error_count_24h: jobSourceStats.error_count_24h,
              last_error_at: jobSourceStats.last_error_at,
              rate_limit_info: jobSourceStats.rate_limit_info,

              // Combined data (prefer database stats when available)
              last_fetch_at:
                jobSourceStats.last_fetch_at || latestRun?.startedAt || null,
              success_rate:
                jobSourceStats.success_rate !== undefined
                  ? jobSourceStats.success_rate
                  : stats.successRate / 100,
              last_error: stats.lastError,

              // Apify-specific metrics for reference
              total_runs: actorData?.stats?.totalRuns || 0,
              last_run_status: latestRun?.status || null,
            },
            schedule: {
              id: scheduleData?.id || null,
              name: scheduleData?.name || null,
              title: scheduleData?.title || null,
              cronExpression: scheduleData?.cronExpression || null,
              timezone: scheduleData?.timezone || null,
              isEnabled: scheduleData?.isEnabled,
              nextRunAt: scheduleData?.nextRunAt || null,
              lastRunAt: scheduleData?.lastRunAt || null,
              apifyUrl: scheduleData
                ? `https://console.apify.com/schedules/${scheduleData.id}`
                : null,
            },
            config: {
              apify_actor_url: `https://console.apify.com/actors/${actorId}`,
            },
          };
        } catch (error) {
          logger.error(
            `Failed to fetch data for actor ${actorConfig.id}:`,
            error
          );
          return {
            id: actorConfig.id,
            name: actorConfig.name,
            description: `${actorConfig.description} (Error)`,
            enabled: false,
            actor_id: actorId || '',
            endpoint: actorConfig.endpoint,
            stats: {
              total_jobs_fetched: 0,
              jobs_fetched_today: 0,
              avg_response_time_ms: 0,
              error_count_24h: 1,
              last_error_at: new Date().toISOString(),
              rate_limit_info: {},
              last_fetch_at: null,
              success_rate: 0,
              last_error:
                error instanceof Error ? error.message : 'Unknown error',
              total_runs: 0,
              last_run_status: null,
            },
            schedule: {
              id: null,
              name: null,
              title: null,
              cronExpression: null,
              timezone: null,
              isEnabled: false,
              nextRunAt: null,
              lastRunAt: null,
              apifyUrl: null,
            },
            config: {
              apify_actor_url: actorId
                ? `https://console.apify.com/actors/${actorId}`
                : '',
            },
          };
        }
      })
    );

    return NextResponse.json({
      success: true,
      sources: sourcesWithStats,
    });
  } catch (error) {
    logger.error('Failed to fetch job sources:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job sources' },
      { status: 500 }
    );
  }
}

async function fetchActorDetails(
  apifyToken: string,
  actorId: string
): Promise<ApifyActor | null> {
  try {
    const response = await fetch(`https://api.apify.com/v2/acts/${actorId}`, {
      headers: {
        Authorization: `Bearer ${apifyToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(
        `Apify API error: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    logger.error(`Failed to fetch actor ${actorId}:`, error);
    return null;
  }
}

async function fetchActorRuns(
  apifyToken: string,
  actorId: string,
  limit = 10
): Promise<ApifyActorRun[]> {
  try {
    const response = await fetch(
      `https://api.apify.com/v2/acts/${actorId}/runs?limit=${limit}&desc=true`,
      {
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        `Apify API error: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    const runs = data.data?.items || [];

    // For the latest run, fetch full details to get defaultDatasetId
    if (runs.length > 0 && runs[0]?.id) {
      try {
        const latestRunResponse = await fetch(
          `https://api.apify.com/v2/acts/${actorId}/runs/${runs[0].id}`,
          {
            headers: {
              Authorization: `Bearer ${apifyToken}`,
              'Content-Type': 'application/json',
            },
          }
        );

        if (latestRunResponse.ok) {
          const latestRunData = await latestRunResponse.json();
          runs[0] = latestRunData.data; // Replace with detailed run info
        }
      } catch (error) {
        logger.warn(
          `Failed to fetch detailed run info for ${runs[0].id}:`,
          error
        );
        // Continue with basic run data
      }
    }

    return runs;
  } catch (error) {
    logger.error(`Failed to fetch runs for actor ${actorId}:`, error);
    return [];
  }
}

async function fetchActorSchedule(apifyToken: string, actorId: string) {
  try {
    const response = await fetch('https://api.apify.com/v2/schedules', {
      headers: {
        Authorization: `Bearer ${apifyToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      logger.warn(`Failed to fetch schedules: ${response.status}`);
      return null;
    }

    const data = await response.json();
    const schedules = data.data?.items || [];

    // Find schedule for this actor
    const schedule = schedules.find(
      (s: { actions?: { actorId: string }[] }) =>
        s.actions?.[0]?.actorId === actorId
    );

    if (!schedule) {
      return null;
    }

    return {
      id: schedule.id,
      name: schedule.name,
      title: schedule.title,
      cronExpression: schedule.cronExpression,
      timezone: schedule.timezone,
      isEnabled: schedule.isEnabled,
      nextRunAt: schedule.nextRunAt,
      lastRunAt: schedule.lastRunAt,
      apifyUrl: `https://console.apify.com/schedules/${schedule.id}`,
    };
  } catch (error) {
    logger.error(`Failed to fetch schedule for actor ${actorId}:`, error);
    return null;
  }
}

function calculateActorStats(runs: ApifyActorRun[]) {
  if (runs.length === 0) {
    return {
      successRate: 0,
      lastError: null,
    };
  }

  // Calculate success rate
  const successfulRuns = runs.filter(
    (run) => run.status === 'SUCCEEDED'
  ).length;
  const successRate = (successfulRuns / runs.length) * 100;

  // Get last error
  const lastFailedRun = runs.find(
    (run) => run.status === 'FAILED' || run.status === 'ABORTED'
  );
  const lastError = lastFailedRun?.statusMessage || null;

  return {
    successRate: Math.round(successRate),
    lastError,
  };
}
