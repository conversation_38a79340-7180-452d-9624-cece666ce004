import { NextResponse } from 'next/server';

export function GET() {
  // Security check - only allow in development or with secret
  const isDev = process.env.NODE_ENV === 'development';
  const hasSecret = process.env.DEBUG_SECRET === 'debug-env-check';

  if (!(isDev || hasSecret)) {
    return NextResponse.json({ error: 'Not authorized' }, { status: 403 });
  }

  return NextResponse.json({
    environment: process.env.NODE_ENV,
    qstash: {
      token: process.env.QSTASH_TOKEN ? '✅ Present' : '❌ Missing',
      currentSigningKey: process.env.QSTASH_CURRENT_SIGNING_KEY
        ? '✅ Present'
        : '❌ Missing',
      nextSigningKey: process.env.QSTASH_NEXT_SIGNING_KEY
        ? '✅ Present'
        : '❌ Missing',
      tokenLength: process.env.QSTASH_TOKEN?.length || 0,
      currentKeyLength: process.env.QSTASH_CURRENT_SIGNING_KEY?.length || 0,
      nextKeyLength: process.env.QSTASH_NEXT_SIGNING_KEY?.length || 0,
    },
    other: {
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
    },
  });
}
