import { NextResponse } from 'next/server';
import { logger } from '@/lib/utils';

/**
 * GET /api/airtable-config
 * Returns Airtable configuration from environment variables
 * (excluding sensitive PAT token)
 */
export function GET() {
  try {
    const config = {
      hasConfig: !!(
        process.env.AIRTABLE_PAT &&
        process.env.AIRTABLE_BASE_ID &&
        process.env.AIRTABLE_TABLE_NAME
      ),
      baseId: process.env.AIRTABLE_BASE_ID || null,
      tableName: process.env.AIRTABLE_TABLE_NAME || null,
      // Never expose the PAT token to the frontend
      hasPat: !!process.env.AIRTABLE_PAT,
    };

    return NextResponse.json(config);
  } catch (error) {
    logger.error('Airtable config error:', error);
    return NextResponse.json(
      { error: 'Failed to load Airtable configuration' },
      { status: 500 }
    );
  }
}
