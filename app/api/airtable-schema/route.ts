import { NextResponse } from 'next/server';
import { logger } from '@/lib/utils';

// Airtable schema types
interface AirtableField {
  id: string;
  name: string;
  type: string;
  options?: unknown;
}

interface AirtableTable {
  id: string;
  name: string;
  fields: AirtableField[];
}

interface AirtableBaseResponse {
  tables: AirtableTable[];
}

/**
 * GET /api/airtable-schema
 * Fetches the table schema from Airtable to see available fields
 */
export async function GET() {
  try {
    // Check environment configuration
    const pat = process.env.AIRTABLE_PAT;
    const baseId = process.env.AIRTABLE_BASE_ID;
    const tableName = process.env.AIRTABLE_TABLE_NAME;

    if (!(pat && baseId && tableName)) {
      return NextResponse.json(
        {
          error:
            'Airtable not configured. Please set AIRTABLE_PAT, AIRTABLE_BASE_ID, and AIRTABLE_TABLE_NAME environment variables.',
        },
        { status: 400 }
      );
    }

    // Fetch base metadata to get table schema
    const response = await fetch(
      `https://api.airtable.com/v0/meta/bases/${baseId}/tables`,
      {
        headers: {
          Authorization: `Bearer ${pat}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.text();
      logger.error('Airtable API error:', response.status, errorData);
      return NextResponse.json(
        {
          error: `Airtable API error: ${response.status} ${response.statusText}`,
          details: errorData,
        },
        { status: response.status }
      );
    }

    const data = (await response.json()) as AirtableBaseResponse;

    // Find the specific table we're interested in
    const targetTable = data.tables.find((table) => table.name === tableName);

    if (!targetTable) {
      return NextResponse.json(
        {
          error: `Table "${tableName}" not found in base`,
          availableTables: data.tables.map((t) => t.name),
        },
        { status: 404 }
      );
    }

    // Return the table schema with field details
    return NextResponse.json({
      success: true,
      tableName: targetTable.name,
      tableId: targetTable.id,
      fields: targetTable.fields.map((field) => ({
        id: field.id,
        name: field.name,
        type: field.type,
        options: field.options,
      })),
      allTables: data.tables.map((t) => t.name),
    });
  } catch (error) {
    logger.error('Error fetching Airtable schema:', error);
    return NextResponse.json(
      { error: 'Failed to fetch schema', details: error },
      { status: 500 }
    );
  }
}
