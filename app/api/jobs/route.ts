import { createClient as createAdminClient } from "@supabase/supabase-js";
import { type NextRequest, NextResponse } from "next/server";
import { createClient } from "../../../lib/supabase";

// Initialize Supabase client
const supabase = createClient();

// Admin client with service role (for privileged operations)
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.SUPABASE_SERVICE_ROLE_KEY || ""
);

// Helper function to parse pagination parameters
function parsePaginationParams(searchParams: URLSearchParams) {
  const page = Math.max(
    1,
    Number.parseInt(searchParams.get("page") || "1", 10)
  );
  const limit = Math.min(
    500,
    Math.max(1, Number.parseInt(searchParams.get("limit") || "25", 10))
  );
  const offset = (page - 1) * limit;

  return { page, limit, offset };
}

// Helper function to parse filter parameters
function parseFilterParams(searchParams: URLSearchParams) {
  const filters = {
    search: searchParams.get("search") || "",
    status: searchParams.get("status") || "",
    processingStatus: searchParams.get("processingStatus") || "",
    sourceType: searchParams.get("sourceType") || "",
    workplaceType: searchParams.get("workplaceType") || "",
    // Advanced filters
    jobTypes:
      searchParams
        .get("jobTypes")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
    workplaceTypes:
      searchParams
        .get("workplaceTypes")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
    careerLevels:
      searchParams
        .get("careerLevels")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
    salaryMin: searchParams.get("salaryMin")
      ? Number(searchParams.get("salaryMin"))
      : undefined,
    salaryMax: searchParams.get("salaryMax")
      ? Number(searchParams.get("salaryMax"))
      : undefined,
    salaryCurrencies:
      searchParams
        .get("salaryCurrencies")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
    includeKeywords:
      searchParams
        .get("includeKeywords")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
    excludeKeywords:
      searchParams
        .get("excludeKeywords")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
    countries:
      searchParams
        .get("countries")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
    languages:
      searchParams
        .get("languages")
        ?.split(",")
        .map((s) => s.trim())
        .filter(Boolean) || [],
  };

  return filters;
}

// Helper function to parse sorting parameters
function parseSortingParams(searchParams: URLSearchParams) {
  const sortField = searchParams.get("sortField") || "created_at";
  const sortDirection =
    searchParams.get("sortDirection") === "asc" ? "asc" : "desc";

  return { sortField, sortDirection };
}

// biome-ignore lint/suspicious/noExplicitAny: Supabase query builders require dynamic types
function applySearchFilters(inputQuery: any, filters: any) {
  let query = inputQuery;
  if (filters.search) {
    query = query.or(
      `title.ilike.%${filters.search}%,company.ilike.%${filters.search}%,description.ilike.%${filters.search}%`
    );
  }
  return query;
}

// biome-ignore lint/suspicious/noExplicitAny: Supabase query builders require dynamic types
function applyStatusFilters(inputQuery: any, filters: any) {
  let query = inputQuery;
  if (filters.status) {
    query = query.eq("status", filters.status);
  }

  if (filters.processingStatus === "pending") {
    query = query.eq("processing_status", "pending");
  } else if (filters.processingStatus === "completed") {
    query = query.eq("processing_status", "completed");
  } else if (filters.processingStatus === "failed") {
    query = query.eq("processing_status", "failed");
  }

  return query;
}

// biome-ignore lint/suspicious/noExplicitAny: Supabase query builders require dynamic types
function applyJobTypeFilters(inputQuery: any, filters: any) {
  let query = inputQuery;
  if (filters.workplaceType) {
    query = query.eq("workplace_type", filters.workplaceType);
  }

  if (filters.jobTypes && filters.jobTypes.length > 0) {
    query = query.in("type", filters.jobTypes);
  }

  if (filters.workplaceTypes && filters.workplaceTypes.length > 0) {
    query = query.in("workplace_type", filters.workplaceTypes);
  }

  if (filters.careerLevels && filters.careerLevels.length > 0) {
    query = query.overlaps("career_level", filters.careerLevels);
  }

  return query;
}

// biome-ignore lint/suspicious/noExplicitAny: Supabase query builders require dynamic types
function applySalaryFilters(inputQuery: any, filters: any) {
  let query = inputQuery;
  if (filters.salaryMin !== undefined) {
    query = query.gte("salary_min", filters.salaryMin);
  }

  if (filters.salaryMax !== undefined) {
    query = query.lte("salary_max", filters.salaryMax);
  }

  if (filters.salaryCurrencies && filters.salaryCurrencies.length > 0) {
    query = query.in("salary_currency", filters.salaryCurrencies);
  }

  return query;
}

// biome-ignore lint/suspicious/noExplicitAny: Supabase query builders require dynamic types
function applyKeywordFilters(inputQuery: any, filters: any) {
  let query = inputQuery;
  if (filters.includeKeywords && filters.includeKeywords.length > 0) {
    // Build OR conditions for include keywords
    const keywordConditions = filters.includeKeywords
      .map(
        (keyword: string) =>
          `title.ilike.%${keyword}%,description.ilike.%${keyword}%,skills.ilike.%${keyword}%`
      )
      .join(",");
    query = query.or(keywordConditions);
  }

  if (filters.excludeKeywords && filters.excludeKeywords.length > 0) {
    // Build NOT conditions for exclude keywords
    for (const keyword of filters.excludeKeywords) {
      query = query
        .not("title", "ilike", `%${keyword}%`)
        .not("description", "ilike", `%${keyword}%`)
        .not("skills", "ilike", `%${keyword}%`);
    }
  }

  return query;
}

// biome-ignore lint/suspicious/noExplicitAny: Supabase query builders require dynamic types
function applyLocationFilters(inputQuery: any, filters: any) {
  let query = inputQuery;
  if (filters.countries && filters.countries.length > 0) {
    query = query.in("workplace_country", filters.countries);
  }

  if (filters.languages && filters.languages.length > 0) {
    query = query.overlaps("languages", filters.languages);
  }

  return query;
}

// biome-ignore lint/suspicious/noExplicitAny: gradual typing for complex Supabase queries
function applyFilters(query: unknown, filters: any) {
  // Cast to dynamic to build query since Supabase types are complex
  // biome-ignore lint/suspicious/noExplicitAny: gradual typing
  let filteredQuery = query as any;

  // Apply all filter categories
  filteredQuery = applySearchFilters(filteredQuery, filters);
  filteredQuery = applyStatusFilters(filteredQuery, filters);
  filteredQuery = applyJobTypeFilters(filteredQuery, filters);
  filteredQuery = applySalaryFilters(filteredQuery, filters);
  filteredQuery = applyKeywordFilters(filteredQuery, filters);
  filteredQuery = applyLocationFilters(filteredQuery, filters);

  return filteredQuery;
}

function applySorting(
  query: unknown,
  sortField: string,
  sortDirection: string
) {
  const validSortFields = [
    "title",
    "company",
    "status",
    "created_at",
    "updated_at",
    "posted_date",
  ];
  const actualSortField = validSortFields.includes(sortField)
    ? sortField
    : "created_at";

  // biome-ignore lint/suspicious/noExplicitAny: dynamic builder
  return (query as any).order(actualSortField, {
    ascending: sortDirection === "asc",
  });
}

// Helper functions for generating readable SQL
// biome-ignore lint/suspicious/noExplicitAny: Dynamic filter structure
function buildSearchConditions(filters: any, conditions: string[]) {
  if (filters.search) {
    conditions.push(`(
      title ILIKE '%${filters.search}%' OR 
      company ILIKE '%${filters.search}%' OR 
      description ILIKE '%${filters.search}%'
    )`);
  }
}

// biome-ignore lint/suspicious/noExplicitAny: Dynamic filter structure
function buildStatusConditions(filters: any, conditions: string[]) {
  if (filters.status) {
    conditions.push(`status = '${filters.status}'`);
  }
  if (filters.processingStatus) {
    conditions.push(`processing_status = '${filters.processingStatus}'`);
  }
}

// biome-ignore lint/suspicious/noExplicitAny: Dynamic filter structure
function buildJobTypeConditions(filters: any, conditions: string[]) {
  if (filters.workplaceType) {
    conditions.push(`workplace_type = '${filters.workplaceType}'`);
  }
  if (filters.jobTypes && filters.jobTypes.length > 0) {
    conditions.push(
      `type IN (${filters.jobTypes.map((t: string) => `'${t}'`).join(", ")})`
    );
  }
  if (filters.workplaceTypes && filters.workplaceTypes.length > 0) {
    conditions.push(
      `workplace_type IN (${filters.workplaceTypes
        .map((t: string) => `'${t}'`)
        .join(", ")})`
    );
  }
  if (filters.careerLevels && filters.careerLevels.length > 0) {
    conditions.push(
      `career_level && ARRAY[${filters.careerLevels
        .map((l: string) => `'${l}'`)
        .join(", ")}]`
    );
  }
}

// biome-ignore lint/suspicious/noExplicitAny: Dynamic filter structure
function buildSalaryConditions(filters: any, conditions: string[]) {
  if (filters.salaryMin !== undefined) {
    conditions.push(`salary_min >= ${filters.salaryMin}`);
  }
  if (filters.salaryMax !== undefined) {
    conditions.push(`salary_max <= ${filters.salaryMax}`);
  }
  if (filters.salaryCurrencies && filters.salaryCurrencies.length > 0) {
    conditions.push(
      `salary_currency IN (${filters.salaryCurrencies
        .map((c: string) => `'${c}'`)
        .join(", ")})`
    );
  }
}

// biome-ignore lint/suspicious/noExplicitAny: Dynamic filter structure
function buildKeywordConditions(filters: any, conditions: string[]) {
  if (filters.includeKeywords && filters.includeKeywords.length > 0) {
    const keywordConditions = filters.includeKeywords.map(
      (keyword: string) =>
        `(title ILIKE '%${keyword}%' OR description ILIKE '%${keyword}%' OR skills ILIKE '%${keyword}%')`
    );
    conditions.push(`(${keywordConditions.join(" OR ")})`);
  }
  if (filters.excludeKeywords && filters.excludeKeywords.length > 0) {
    for (const keyword of filters.excludeKeywords) {
      conditions.push(
        `NOT (title ILIKE '%${keyword}%' OR description ILIKE '%${keyword}%' OR skills ILIKE '%${keyword}%')`
      );
    }
  }
}

// biome-ignore lint/suspicious/noExplicitAny: Dynamic filter structure
function buildLocationConditions(filters: any, conditions: string[]) {
  if (filters.countries && filters.countries.length > 0) {
    conditions.push(
      `workplace_country IN (${filters.countries
        .map((c: string) => `'${c}'`)
        .join(", ")})`
    );
  }
  if (filters.languages && filters.languages.length > 0) {
    conditions.push(
      `languages && ARRAY[${filters.languages
        .map((l: string) => `'${l}'`)
        .join(", ")}]`
    );
  }
}

// Function to generate readable SQL for display in SQL Query tab
// biome-ignore lint/suspicious/noExplicitAny: Dynamic filter structure
function generateReadableSQL(filters: any): string {
  const conditions: string[] = [];
  const baseQuery = "SELECT * FROM jobs";

  // Base conditions
  conditions.push("status = 'active'");

  // Build all filter conditions using helper functions
  buildSearchConditions(filters, conditions);
  buildStatusConditions(filters, conditions);
  buildJobTypeConditions(filters, conditions);
  buildSalaryConditions(filters, conditions);
  buildKeywordConditions(filters, conditions);
  buildLocationConditions(filters, conditions);

  // Build the complete query
  const whereClause =
    conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";
  const orderClause = "ORDER BY created_at DESC";
  const limitClause = "LIMIT 50";

  return `${baseQuery}\n${whereClause}\n${orderClause}\n${limitClause}`;
}

export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();
    const { searchParams } = new URL(request.url);

    const { page, limit, offset } = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);
    const { sortField, sortDirection } = parseSortingParams(searchParams);

    // Build the query
    let query = supabase.from("jobs").select("*", { count: "exact" });

    // Apply filters and sorting
    query = applyFilters(query, filters);
    query = applySorting(query, sortField, sortDirection);

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: jobs, error, count } = await query;

    if (error) {
      throw error;
    }

    const totalPages = Math.ceil((count || 0) / limit);

    // biome-ignore lint/suspicious/noExplicitAny: dynamic response shape varies
    const response: any = {
      jobs: jobs || [],
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: count || 0,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
      filters,
      sorting: {
        field: sortField,
        direction: sortDirection,
      },
    };

    // Add SQL query information for debugging and UI display
    // biome-ignore lint/suspicious/noExplicitAny: filter counting
    const filtersAny = filters as any;
    response.sqlInfo = {
      executionTime: Date.now() - startTime,
      filterCount: Object.keys(filters).filter(
        (key) =>
          filtersAny[key] !== undefined &&
          filtersAny[key] !== "" &&
          !(Array.isArray(filtersAny[key]) && filtersAny[key].length === 0)
      ).length,
      totalJobs: count || 0,
      filteredJobs: jobs?.length || 0,
      query: generateReadableSQL(filters),
    };

    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      {
        error: "Failed to fetch jobs",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const ids: string[] | undefined = body?.ids;
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: "ids array is required" },
        { status: 400 }
      );
    }

    const { error } = await supabaseAdmin.from("jobs").delete().in("id", ids);
    if (error) {
      throw error;
    }

    return NextResponse.json({ success: true, deleted: ids.length });
  } catch (error) {
    return NextResponse.json(
      {
        error: "Failed to delete jobs",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: API route handles multiple bulk actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    if (body.action === "updateStatus") {
      // Implementation for updating a single job status
    } else if (body.action === "bulkUpdateStatus") {
      const { ids, status } = body as {
        ids?: string[];
        status?: string;
      };
      if (!ids || ids.length === 0 || !status) {
        return NextResponse.json(
          { error: "ids array and status are required" },
          { status: 400 }
        );
      }

      const { error } = await supabaseAdmin
        .from("jobs")
        .update({ status })
        .in("id", ids);
      if (error) {
        throw error;
      }

      return NextResponse.json({ success: true, updated: ids.length });
    } else if (body.action === "bulkRequeueExtraction") {
      const { ids } = body as { ids?: string[] };
      if (!ids || ids.length === 0) {
        return NextResponse.json(
          { error: "ids array is required" },
          { status: 400 }
        );
      }
      const { error } = await supabaseAdmin
        .from("jobs")
        .update({ processing_status: "pending" })
        .in("id", ids);
      if (error) {
        throw error;
      }
      return NextResponse.json({ success: true, updated: ids.length });
    } else if (body.action === "processNow") {
      const { id } = body as { id?: string };
      if (!id) {
        return NextResponse.json(
          { error: "Job ID is required" },
          { status: 400 }
        );
      }

      // First, get the job data to process
      const { data: job, error: fetchError } = await supabaseAdmin
        .from("jobs")
        .select("*")
        .eq("id", id)
        .single();

      if (fetchError || !job) {
        return NextResponse.json({ error: "Job not found" }, { status: 404 });
      }

      // Process the job immediately using the extract API
      try {
        const extractResponse = await fetch(
          `${
            process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"
          }/api/extract`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              jobId: id,
              sourceUrl: job.source_url || job.apply_url || "",
              content: job.description || job.title || "",
            }),
          }
        );

        if (!extractResponse.ok) {
          const errorData = await extractResponse.json().catch(() => ({}));
          throw new Error(errorData.error || "AI processing failed");
        }

        const extractResult = await extractResponse.json();

        return NextResponse.json({
          success: true,
          message: "Job processed successfully",
          jobId: id,
          result: extractResult,
        });
      } catch (error) {
        // If processing fails, log the error but don't fail the request
        console.error("Failed to process job:", error);
        return NextResponse.json(
          {
            error: error instanceof Error ? error.message : "Processing failed",
            jobId: id,
          },
          { status: 500 }
        );
      }
    } else if (body.action === "bulkResetMonitor") {
      const { ids } = body as { ids?: string[] };
      if (!ids || ids.length === 0) {
        return NextResponse.json(
          { error: "ids array is required" },
          { status: 400 }
        );
      }
      const { error } = await supabaseAdmin
        .from("jobs")
        .update({
          monitor_attempts: 0,
          next_try_at: null,
          monitor_status: null,
        })
        .in("id", ids);
      if (error) {
        throw error;
      }
      return NextResponse.json({ success: true, updated: ids.length });
    } else if (body.action === "bulkProcessNow") {
      const { ids } = body as { ids?: string[] };
      if (!ids || ids.length === 0) {
        return NextResponse.json(
          { error: "ids array is required" },
          { status: 400 }
        );
      }

      // Validate that all IDs are valid UUIDs
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      const invalidIds = ids.filter((id) => !uuidRegex.test(id));
      if (invalidIds.length > 0) {
        return NextResponse.json(
          { error: `Invalid UUID format for IDs: ${invalidIds.join(", ")}` },
          { status: 400 }
        );
      }

      // Fetch all jobs to process
      const { data: jobsData, error: fetchError } = await supabaseAdmin
        .from("jobs")
        .select("*")
        .in("id", ids);

      if (fetchError) {
        throw fetchError;
      }

      if (!jobsData || jobsData.length === 0) {
        return NextResponse.json(
          { error: "No jobs found with provided IDs" },
          { status: 404 }
        );
      }

      // Determine base URL (env var takes precedence, else derive from request)
      const baseUrl =
        process.env.NEXT_PUBLIC_SITE_URL || request.nextUrl.origin || "";

      if (!baseUrl) {
        return NextResponse.json(
          {
            error:
              "Unable to determine base URL. Set NEXT_PUBLIC_SITE_URL env var.",
          },
          { status: 500 }
        );
      }

      const results = [];
      const errors = [];

      // Process each job sequentially to avoid overwhelming the extract API
      for (const job of jobsData) {
        try {
          const extractResponse = await fetch(`${baseUrl}/api/extract`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              jobId: job.id,
              sourceUrl: job.source_url || job.apply_url || "",
              content: job.description || job.title || "",
            }),
          });

          if (!extractResponse.ok) {
            const errorData = await extractResponse.json().catch(() => ({}));
            throw new Error(errorData.error || "AI processing failed");
          }

          const extractResult = await extractResponse.json();
          results.push({
            jobId: job.id,
            success: true,
            result: extractResult,
          });
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : "Processing failed";
          errors.push({
            jobId: job.id,
            error: errorMessage,
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: `Bulk processing completed: ${results.length} successful, ${errors.length} failed`,
        results,
        errors: errors.length > 0 ? errors : undefined,
        stats: {
          total: jobsData.length,
          successful: results.length,
          failed: errors.length,
        },
      });
    } else if (body.action === "bulkPushAirtable") {
      const { ids, limit = 20 } = body as { ids?: string[]; limit?: number };
      if (!ids || ids.length === 0) {
        return NextResponse.json(
          { error: "ids array is required" },
          { status: 400 }
        );
      }
      const sliceIds = ids.slice(0, limit);
      const { data: jobsData, error } = await supabaseAdmin
        .from("jobs")
        .select("*")
        .in("id", sliceIds);
      if (error) {
        throw error;
      }

      // Determine base URL (env var takes precedence, else derive from request)
      const baseUrl =
        process.env.NEXT_PUBLIC_BASE_URL || request.nextUrl.origin || "";

      if (!baseUrl) {
        throw new Error(
          "Unable to determine base URL. Set NEXT_PUBLIC_BASE_URL env var."
        );
      }

      let pushed = 0;
      const successfulSyncs: Array<{ jobId: string; recordId: string }> = [];

      for (const job of jobsData || []) {
        // Send sequentially to airtable-send route with small delay
        // biome-ignore lint/nursery/noAwaitInLoop: Sequential processing required for rate limiting
        const airtableRes = await fetch(`${baseUrl}/api/airtable-send`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ jobData: job }),
        });

        if (!airtableRes.ok) {
          const errInfo = await airtableRes.json().catch(() => null);
          const errDetails = errInfo?.details
            ? JSON.stringify(errInfo.details)
            : undefined;
          const errMsg = errInfo?.error || airtableRes.statusText;
          throw new Error(
            `Airtable push failed for job ${job.id}: ${errMsg}${
              errDetails ? ` | ${errDetails}` : ""
            }`
          );
        }

        // Capture sync tracking information
        const airtableResult = await airtableRes.json();
        const recordId = airtableResult.recordId;

        if (recordId) {
          successfulSyncs.push({ jobId: job.id, recordId });
        }

        pushed += 1;
        // Simple limiter: 200ms delay
        await new Promise((res) => setTimeout(res, 200));
      }

      // Update sync tracking in database for successful syncs
      if (successfulSyncs.length > 0) {
        const updatePromises = successfulSyncs.map(({ jobId, recordId }) =>
          supabase
            .from("jobs")
            .update({
              airtable_synced_at: new Date().toISOString(),
              airtable_id: recordId,
            })
            .eq("id", jobId)
        );

        await Promise.all(updatePromises);
      }

      return NextResponse.json({
        success: true,
        pushed,
        syncedRecords: successfulSyncs.length,
      });
    } else {
      return NextResponse.json(
        { error: "Unsupported action" },
        { status: 400 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        error: "Failed to process the request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
