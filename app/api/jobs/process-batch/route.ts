import { openai } from '@ai-sdk/openai';
import { generateObject } from 'ai';
import { type NextRequest, NextResponse } from 'next/server';
import { JobExtractionSchema } from '@/lib/job-schema';
import { createClient } from '@/lib/supabase';
import { logger } from '@/lib/utils';

/**
 * QStash Scheduled Job Processor
 *
 * Runs every 5 minutes to process pending jobs:
 * 1. Fetch batch of pending jobs from Supabase
 * 2. Process each with AI extraction
 * 3. Update database with extracted data
 * 4. Mark as completed
 *
 * This is triggered by QStash schedule, not webhooks
 */

// Verify QStash signature for security
function verifyQstashSignature(request: NextRequest): boolean {
  const signature = request.headers.get('upstash-signature');
  return !!signature; // Simple check for now
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Verify this is from QStash
    if (!verifyQstashSignature(request)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    logger.info('⏰ Starting scheduled job processing');

    const supabase = createClient();

    // Fetch pending jobs (limit batch size for processing time)
    const { data: pendingJobs, error: fetchError } = await supabase
      .from('jobs')
      .select('*')
      .eq('processing_status', 'pending')
      .order('created_at', { ascending: true })
      .limit(10); // Process 10 jobs per run

    if (fetchError) {
      logger.error('Failed to fetch pending jobs', { error: fetchError });
      throw fetchError;
    }

    if (!pendingJobs || pendingJobs.length === 0) {
      logger.info('No pending jobs to process');
      return NextResponse.json({
        success: true,
        message: 'No pending jobs',
        processed: 0,
      });
    }

    logger.info(`📋 Found ${pendingJobs.length} pending jobs to process`);

    // Process all jobs in parallel for better performance
    const jobProcessingPromises = pendingJobs.map(async (job) => {
      try {
        logger.info(`🤖 Processing job ${job.id}`, {
          title: job.title,
          source: job.source_type,
        });

        // Extract structured data using AI
        const { object: extractedData } = await generateObject({
          model: openai('gpt-4o-mini'),
          schema: JobExtractionSchema,
          prompt: `Extract structured job information from this job posting:\n\nTitle: ${job.title}\n\nDescription: ${job.description}\n\nSource: ${job.source_type}`,
        });

        // Update job with extracted data
        const { error: updateError } = await supabase
          .from('jobs')
          .update({
            ...extractedData,
            processing_status: 'completed',
            processed_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq('id', job.id);

        if (updateError) {
          throw updateError;
        }

        logger.info(`✅ Successfully processed job ${job.id}`, {
          company: extractedData.company,
          workplace_city: extractedData.workplace_city,
        });

        return { success: true, jobId: job.id };
      } catch (error) {
        // Better error logging
        logger.error(`🚨 Detailed error for job ${job.id}:`, {
          error,
          errorType: typeof error,
          errorConstructor: error?.constructor?.name,
          errorMessage: error instanceof Error ? error.message : String(error),
          jobId: job.id,
          jobTitle: job.title,
          jobContent: `${job.description?.substring(0, 200)}...`,
        });

        const errorMessage =
          error instanceof Error ? error.message : String(error);

        logger.error(`Failed to process job ${job.id}`, {
          error: errorMessage,
        });

        // Mark job as failed
        await supabase
          .from('jobs')
          .update({
            processing_status: 'failed',
            processing_error: errorMessage,
            updated_at: new Date().toISOString(),
          })
          .eq('id', job.id);

        return {
          success: false,
          jobId: job.id,
          error: errorMessage,
        };
      }
    });

    // Wait for all jobs to complete and collect results
    const results = await Promise.all(jobProcessingPromises);

    const successCount = results.filter((r) => r.success).length;
    const errorCount = results.filter((r) => !r.success).length;
    const errors = results
      .filter((r) => !r.success)
      .map((r) => ({ jobId: r.jobId, error: r.error }));

    const processingTime = Date.now() - startTime;

    // Log pipeline metrics
    logger.pipeline({
      step: 'PROCESSED',
      source: 'batch_processor',
      jobCount: successCount,
      success: true,
      duration: processingTime,
      stats: {
        total: pendingJobs.length,
        success: successCount,
        failed: errorCount,
      },
    });

    const response = {
      success: true,
      message: 'Batch processing completed',
      stats: {
        total: pendingJobs.length,
        processed: successCount,
        failed: errorCount,
      },
      processingTime: `${processingTime}ms`,
      errors: errors.length > 0 ? errors : undefined,
    };

    logger.info('✅ Batch processing completed', response);

    return NextResponse.json(response);
  } catch (error) {
    logger.error('Batch processor failed', { error });

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: `${Date.now() - startTime}ms`,
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export function GET() {
  return NextResponse.json({
    service: 'Batch Job Processor',
    status: 'healthy',
    schedule: 'Every 5 minutes via QStash',
    features: [
      'Processes 10 jobs per run',
      'AI extraction with GPT-4o-mini',
      'Automatic retry on failure',
      'Pipeline metrics tracking',
    ],
    environment: {
      hasOpenAI: !!process.env.OPENAI_API_KEY,
      hasSupabase: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasQstash: !!process.env.QSTASH_TOKEN,
    },
  });
}
